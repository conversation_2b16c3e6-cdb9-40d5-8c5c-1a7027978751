import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class DashboardService {
  /**
   * Get dashboard summary with total revenue and trend
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Object>} Summary data with totalRevenue, previousYearRevenue, trendPercentage
   */
  async getDashboardSummary(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = queryParams
        ? `${API_URL}/transactions/dashboard/summary?${queryParams}`
        : `${API_URL}/transactions/dashboard/summary`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching dashboard summary:", error);
      // Return fallback data
      return {
        totalRevenue: 0,
        previousYearRevenue: 0,
        trendPercentage: 0,
      };
    }
  }

  /**
   * Get revenue over time data for charts
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of {month, revenue} objects
   */
  async getRevenueOverTime(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = queryParams
        ? `${API_URL}/transactions/dashboard/revenue-over-time?${queryParams}`
        : `${API_URL}/transactions/dashboard/revenue-over-time`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching revenue over time:", error);
      return [];
    }
  }

  /**
   * Get year-over-year comparison data
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of {month, currentYear, previousYear} objects
   */
  async getYearComparison(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = queryParams
        ? `${API_URL}/transactions/dashboard/year-comparison?${queryParams}`
        : `${API_URL}/transactions/dashboard/year-comparison`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching year comparison:", error);
      return [];
    }
  }

  /**
   * Get revenue by category (medium, channel, property, variant)
   * @param {string} categoryType - Type of category: "medium", "channel", "property", "variant"
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of revenue by category
   */
  async getRevenueByCategory(categoryType = "variant", filters = {}) {
    try {
      const queryParams = this.buildQueryParams({ ...filters, categoryType });
      const url = queryParams
        ? `${API_URL}/transactions/dashboard/revenue-by-category?${queryParams}`
        : `${API_URL}/transactions/dashboard/revenue-by-category?categoryType=${categoryType}`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching revenue by category:", error);
      return [];
    }
  }

  /**
   * Get top clients by revenue
   * @param {number} limit - Number of top clients to return
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of {company, revenue, trend} objects
   */
  async getTopClients(limit = 5, filters = {}) {
    try {
      const queryParams = this.buildQueryParams({ ...filters, limit });
      const url = queryParams
        ? `${API_URL}/transactions/dashboard/top-clients?${queryParams}`
        : `${API_URL}/transactions/dashboard/top-clients?limit=${limit}`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching top clients:", error);
      return [];
    }
  }

  /**
   * Get filter options for dashboard dropdowns
   * @returns {Promise<Object>} Object with all filter options
   */
  async getFilterOptions() {
    try {
      const response = await axios.get(
        `${API_URL}/transactions/dashboard/filter-options`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching filter options:", error);
      // Return fallback static options
      return {
        years: ["2024", "2023", "2022"],
        months: [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ],
        mediums: ["Print", "Digital", "Outdoor", "Event"],
        channels: ["OA Mag", "New Media", "Events", "R4G", "M4G"],
        properties: [
          "Website",
          "Magazine",
          "Newsletter",
          "Convention",
          "Awards",
        ],
        variants: ["Print", "Website", "Event", "Event Sp", "Seorc", "Mont"],
        cities: [
          "Mumbai",
          "Bengaluru",
          "Delhi",
          "Pune",
          "Chennai",
          "Hyderabad",
        ],
        customers: [
          "Xireme Media",
          "Singpost",
          "SabRentKaro",
          "JBM Group",
          "Sakal",
        ],
      };
    }
  }

  /**
   * Get revenue by medium
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of revenue by medium
   */
  async getRevenueByMedium(filters = {}) {
    return this.getRevenueByCategory("medium", filters);
  }

  /**
   * Get revenue by channel
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of revenue by channel
   */
  async getRevenueByChannel(filters = {}) {
    return this.getRevenueByCategory("channel", filters);
  }

  /**
   * Get revenue by property
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of revenue by property
   */
  async getRevenueByProperty(filters = {}) {
    return this.getRevenueByCategory("property", filters);
  }

  /**
   * Get revenue by variant
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Array of revenue by variant
   */
  async getRevenueByVariant(filters = {}) {
    return this.getRevenueByCategory("variant", filters);
  }

  /**
   * Build query parameters string from filters object
   * @param {Object} filters - Filter parameters
   * @returns {string} Query parameters string
   */
  buildQueryParams(filters) {
    const params = new URLSearchParams();

    // Add all supported filter parameters
    if (filters.year) params.append("year", filters.year);
    if (filters.month) params.append("month", filters.month);
    if (filters.customerId) params.append("customerId", filters.customerId);
    if (filters.mediumId) params.append("mediumId", filters.mediumId);
    if (filters.channelId) params.append("channelId", filters.channelId);
    if (filters.propertyId) params.append("propertyId", filters.propertyId);
    if (filters.variantId) params.append("variantId", filters.variantId);
    if (filters.cityId) params.append("cityId", filters.cityId);
    if (filters.categoryType)
      params.append("categoryType", filters.categoryType);
    if (filters.limit) params.append("limit", filters.limit);

    return params.toString();
  }

  /**
   * Get all dashboard data at once using the new consolidated API
   * @param {Object} filters - Filter parameters with arrays
   * @returns {Promise<Object>} Object with all dashboard data
   */
  async getAllDashboardData(filters = {}) {
    try {
      // Build the request body with filter arrays
      const requestBody = this.buildFilterRequestBody(filters);

      const response = await axios.post(
        `${API_URL}/transactions/dashboard/all-data`,
        requestBody
      );

      // The API returns all data in a single response
      return response.data;
    } catch (error) {
      console.error("Error fetching all dashboard data:", error);

      // Return fallback data structure
      return {
        summary: {
          totalRevenue: 0,
          previousYearRevenue: 0,
          trendPercentage: 0,
        },
        revenueOverTime: [],
        yearComparison: [],
        revenueByVariant: [],
        topClients: [],
        filterOptions: {
          years: ["2024", "2023", "2022"],
          months: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
          ],
          mediums: ["Print", "Digital", "Outdoor", "Event"],
          channels: ["OA Mag", "New Media", "Events", "R4G", "M4G"],
          properties: [
            "Website",
            "Magazine",
            "Newsletter",
            "Convention",
            "Awards",
          ],
          variants: ["Print", "Website", "Event", "Event Sp", "Seorc", "Mont"],
          cities: [
            "Mumbai",
            "Bengaluru",
            "Delhi",
            "Pune",
            "Chennai",
            "Hyderabad",
          ],
          customers: [
            "Xireme Media",
            "Singpost",
            "SabRentKaro",
            "JBM Group",
            "Sakal",
          ],
          contactStatus: ["Active", "Inactive", "Pending"],
          contactCategories: ["Solution Provider", "Brand", "Agency"],
          stages: [
            "Inquiry",
            "Attendee",
            "Proposal",
            "Negotiation",
            "Confirmed",
            "Completed",
            "Cancelled",
          ],
        },
      };
    }
  }

  /**
   * Build request body for the consolidated API with filter arrays
   * @param {Object} filters - Filter parameters from frontend
   * @returns {Object} Request body for the API
   */
  buildFilterRequestBody(filters) {
    const requestBody = {};

    // Map frontend filter names to backend expected names
    if (filters.yearFilters && filters.yearFilters.length > 0) {
      requestBody.yearFilters = filters.yearFilters;
    }

    if (filters.monthFilters && filters.monthFilters.length > 0) {
      requestBody.monthFilters = filters.monthFilters;
    }

    if (filters.mediumFilters && filters.mediumFilters.length > 0) {
      requestBody.mediumFilters = filters.mediumFilters;
    }

    if (filters.channelFilters && filters.channelFilters.length > 0) {
      requestBody.channelFilters = filters.channelFilters;
    }

    if (filters.variantFilters && filters.variantFilters.length > 0) {
      requestBody.variantFilters = filters.variantFilters;
    }

    if (filters.cityFilters && filters.cityFilters.length > 0) {
      requestBody.cityFilters = filters.cityFilters;
    }

    if (filters.contactFilters && filters.contactFilters.length > 0) {
      requestBody.contactFilters = filters.contactFilters;
    }

    if (
      filters.contactStatusFilters &&
      filters.contactStatusFilters.length > 0
    ) {
      requestBody.contactStatusFilters = filters.contactStatusFilters;
    }

    if (filters.stageFilters && filters.stageFilters.length > 0) {
      requestBody.stageFilters = filters.stageFilters;
    }

    return requestBody;
  }

  /**
   * Get all analytics data for the analytics page
   * @param {Object} filters - Filter parameters including year, month, view, etc.
   * @returns {Promise<Object>} Complete analytics data
   */
  async getAnalyticsData(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = queryParams
        ? `${API_URL}/transactions/analytics/all-data?${queryParams}`
        : `${API_URL}/transactions/analytics/all-data`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      // Return fallback data structure
      return {
        status: false,
        message: "Failed to fetch analytics data",
        data: {
          salesDataByYear: {
            year: filters.year || new Date().getFullYear(),
            data: []
          },
          dailyRevenueLast7Days: {
            data: []
          },
          topCustomersByRevenue: {
            limit: 10,
            data: []
          },
          topChannelsByRevenue: {
            limit: 10,
            data: []
          },
          monthlyChannelRevenue: {
            data: []
          },
          allChannelsRevenue: {
            data: []
          }
        }
      };
    }
  }
}

// Create and export a singleton instance
const dashboardService = new DashboardService();
export default dashboardService;
