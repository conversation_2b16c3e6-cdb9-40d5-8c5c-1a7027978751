"use client";

import { useState, useEffect } from "react";
import { Plus, More<PERSON><PERSON><PERSON>tal, <PERSON><PERSON>l, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { StateForm } from "@/components/forms/state-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import stateService from "../../../services/states.service";

export default function State() {
  const { toast } = useToast();
  const [states, setStates] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedState, setSelectedState] = useState(null);

  // Fetch states from API using the service
  const fetchStates = async () => {
    try {
      setIsLoading(true);
      const result = await stateService.getAllStates();

      // Ensure we have an array and handle potential data format issues
      const statesArray = Array.isArray(result) ? result : [];
      setStates(statesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch states",
        variant: "destructive",
      });
      console.error("Error fetching states:", error);
      setStates([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStates();
  }, []);

  const handleAddState = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditState = (state) => {
    setSelectedState(state);
    setIsEditDialogOpen(true);
  };

  const handleDeleteState = (state) => {
    setSelectedState(state);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateState = async (data) => {
    try {
      await stateService.createState(data);
      await fetchStates();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "State created successfully",
      });
    } catch (error) {
      console.error("Create State error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create State",
        variant: "destructive",
      });
    }
  };

  const handleUpdateState = async (data) => {
    try {
      await stateService.updateState(selectedState.id, data);
      await fetchStates();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "State updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update State",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteState = async () => {
    try {
      await stateService.deleteState(selectedState.id);
      await fetchStates();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "State deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete State",
        variant: "destructive",
      });
    }
  };

  const handleImportStates = async (file) => {
    try {
      const result = await stateService.bulkImportStates(file);

      await fetchStates(); // Refresh the states list
      toast({
        title: "Success",
        description: "States imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message || "Failed to import states from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "State Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "country",
      header: "Country",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "active" ? "success" : "secondary"}>
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const state = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditState(state)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteState(state)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>State Management</CardTitle>
              <CardDescription>
                Manage states
              </CardDescription>
            </div>
            <Button onClick={handleAddState}>
              <Plus className="mr-2 h-4 w-4" />
              Add State
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={states}
            searchKey="name"
            isLoading={isLoading}
            entityName="States"
            onImport={handleImportStates}
            templatePath="/templates/states_template.xlsx"
            acceptedFileTypes=".xlsx,.xls,.csv"
            fileDescription="File should contain columns: name, description, status"
          />
        </CardContent>
      </Card>

      {/* Add State Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New State</DialogTitle>
            <DialogDescription>
              Create a new state
            </DialogDescription>
          </DialogHeader>
          <StateForm
            onSubmit={handleCreateState}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit State Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit State</DialogTitle>
            <DialogDescription>
              Update the state information.
            </DialogDescription>
          </DialogHeader>
          {selectedState && (
            <StateForm
              defaultValues={selectedState}
              onSubmit={handleUpdateState}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete State Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedState?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteState}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
