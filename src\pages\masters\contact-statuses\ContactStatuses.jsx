import React, { useState, useEffect } from "react";
import { Plus, Pencil, Trash2, Building } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import contactStatusService from "../../../services/contact-statuses.service";
import { DataTable } from "../../../components/data-table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../../components/ui/card";
import { Button } from "../../../components/ui/button";
import { Badge } from "../../../components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../../components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../components/ui/form";
import { Input } from "../../../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import { useForm } from "react-hook-form";
import { CONTACT_STATUS } from "../../../constants";

export default function ContactStatuses() {
  const { toast } = useToast();
  const [contactStatuses, setContactStatuses] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContactStatus, setSelectedContactStatus] = useState(null);

  // Fetch contact statuses from API using the service
  const fetchContactStatuses = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await contactStatusService.getAllContactStatuses({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const contactStatusesArray = Array.isArray(result.contactStatuses)
        ? result.contactStatuses
        : [];
      setContactStatuses(contactStatusesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch contact statuses",
        variant: "destructive",
      });
      console.error("Error fetching contact statuses:", error);
      setContactStatuses([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchContactStatuses();
  }, []);

  const handleAddContactStatus = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditContactStatus = (contactStatus) => {
    setSelectedContactStatus(contactStatus);
    setIsEditDialogOpen(true);
  };

  const handleDeleteContactStatus = (contactStatus) => {
    setSelectedContactStatus(contactStatus);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateContactStatus = async (contactStatusData) => {
    try {
      await contactStatusService.createContactStatus(contactStatusData);
      toast({
        title: "Success",
        description: "Contact status created successfully",
      });
      setIsAddDialogOpen(false);
      fetchContactStatuses(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create contact status",
        variant: "destructive",
      });
      console.error("Error creating contact status:", error);
    }
  };

  const handleUpdateContactStatus = async (contactStatusData) => {
    try {
      await contactStatusService.updateContactStatus(
        selectedContactStatus.id,
        contactStatusData
      );
      toast({
        title: "Success",
        description: "Contact status updated successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedContactStatus(null);
      fetchContactStatuses(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update contact status",
        variant: "destructive",
      });
      console.error("Error updating contact status:", error);
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      await contactStatusService.deleteContactStatus(selectedContactStatus.id);
      toast({
        title: "Success",
        description: "Contact status deleted successfully",
      });
      setIsDeleteDialogOpen(false);
      setSelectedContactStatus(null);
      fetchContactStatuses(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete contact status",
        variant: "destructive",
      });
      console.error("Error deleting contact status:", error);
    }
  };

  const handleImportContactStatuses = async (file) => {
    try {
      const result = await contactStatusService.importContactStatuses(file);
      await fetchContactStatuses();
      toast({
        title: "Success",
        description: `${
          result.imported || "All"
        } contact statuses imported successfully`,
      });
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message || "Failed to import contact statuses",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Define table columns
  const columns = [
    {
      accessorKey: "name",
      header: "Contact Status Name",
      cell: ({ row }) => (
        <div className="flex items-center">
          <Building className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue("name")}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "active" ? "default" : "secondary"}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("createdAt");
        return date ? new Date(date).toLocaleDateString() : "-";
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const contactStatus = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEditContactStatus(contactStatus)}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleDeleteContactStatus(contactStatus)}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        );
      },
    },
  ];

  // Add Contact Status Form
  const AddContactStatusForm = () => {
    const form = useForm({
      defaultValues: {
        name: "",
        status: "active",
      },
    });

    const onSubmit = (data) => {
      handleCreateContactStatus(data);
    };

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            rules={{ required: "Contact status name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Status Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter contact status name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <DialogFooter>
            <Button type="submit">Create Contact Status</Button>
          </DialogFooter>
        </form>
      </Form>
    );
  };

  // Edit Contact Status Form
  const EditContactStatusForm = () => {
    const form = useForm({
      defaultValues: {
        name: selectedContactStatus?.name || "",
        status: selectedContactStatus?.status || "active",
      },
    });

    const onSubmit = (data) => {
      handleUpdateContactStatus(data);
    };

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            rules={{ required: "Contact status name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contact Status Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter contact status name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <DialogFooter>
            <Button type="submit">Update Contact Status</Button>
          </DialogFooter>
        </form>
      </Form>
    );
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Contact Statuses Management</CardTitle>
              <CardDescription>
                Manage contact statuses for your organization
              </CardDescription>
            </div>
            <Button onClick={handleAddContactStatus}>
              <Plus className="mr-2 h-4 w-4" />
              Add Contact Status
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={contactStatuses}
            searchKey="name"
            isLoading={isLoading}
            entityName="Contact Statuses"
            onImport={handleImportContactStatuses}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/contact_statuses_template.xlsx"
            fileDescription="File should contain columns: name, status"
          />
        </CardContent>
      </Card>

      {/* Add Contact Status Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Contact Status</DialogTitle>
            <DialogDescription>
              Create a new contact status for your organization.
            </DialogDescription>
          </DialogHeader>
          <AddContactStatusForm />
        </DialogContent>
      </Dialog>

      {/* Edit Contact Status Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Contact Status</DialogTitle>
            <DialogDescription>
              Update the details of this contact status.
            </DialogDescription>
          </DialogHeader>
          <EditContactStatusForm />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this contact status? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

