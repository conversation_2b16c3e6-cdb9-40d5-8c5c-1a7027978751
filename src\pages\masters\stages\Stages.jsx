"use client";

import { useState, useEffect } from "react";
import { Plus, MoreHorizontal, Pencil, Trash2, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { StageForm } from "@/components/forms/stage-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import stageService from "../../../services/stages.service";

export default function Stages() {
  const { toast } = useToast();
  const [stages, setStages] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedStage, setSelectedStage] = useState(null);

  // Fetch stages from API using the service
  const fetchStages = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await stageService.getAllStages({ page, limit, search });

      // Ensure we have an array and handle potential data format issues
      const stagesArray = Array.isArray(result.stages) ? result.stages : [];
      setStages(stagesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch stages",
        variant: "destructive",
      });
      console.error("Error fetching stages:", error);
      setStages([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStages();
  }, []);

  const handleAddStage = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditStage = (stage) => {
    setSelectedStage(stage);
    setIsEditDialogOpen(true);
  };

  const handleDeleteStage = (stage) => {
    setSelectedStage(stage);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateStage = async (stageData) => {
    try {
      await stageService.createStage(stageData);
      toast({
        title: "Success",
        description: "Stage created successfully",
      });
      setIsAddDialogOpen(false);
      fetchStages(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create stage",
        variant: "destructive",
      });
      console.error("Error creating stage:", error);
    }
  };

  const handleUpdateStage = async (stageData) => {
    try {
      await stageService.updateStage(selectedStage.id, stageData);
      toast({
        title: "Success",
        description: "Stage updated successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedStage(null);
      fetchStages(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update stage",
        variant: "destructive",
      });
      console.error("Error updating stage:", error);
    }
  };

  const handleConfirmDelete = async () => {
    try {
      await stageService.deleteStage(selectedStage.id);
      toast({
        title: "Success",
        description: "Stage deleted successfully",
      });
      setIsDeleteDialogOpen(false);
      setSelectedStage(null);
      fetchStages(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete stage",
        variant: "destructive",
      });
      console.error("Error deleting stage:", error);
    }
  };

  const handleImportStages = async (file) => {
    try {
      await stageService.importStages(file);
      toast({
        title: "Success",
        description: "Stages imported successfully",
      });
      fetchStages(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to import stages",
        variant: "destructive",
      });
      console.error("Error importing stages:", error);
    }
  };

  // Define table columns
  const columns = [
    {
      accessorKey: "name",
      header: "Stage Name",
      cell: ({ row }) => (
        <div className="flex items-center">
          <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue("name")}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "active" ? "default" : "secondary"}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("createdAt");
        return date ? new Date(date).toLocaleDateString() : "-";
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const stage = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditStage(stage)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteStage(stage)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Stages Management</CardTitle>
              <CardDescription>
                Manage customer stages and workflow statuses
              </CardDescription>
            </div>
            <Button onClick={handleAddStage}>
              <Plus className="mr-2 h-4 w-4" />
              Add Stage
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={stages}
            searchKey="name"
            isLoading={isLoading}
            entityName="Stages"
            onImport={handleImportStages}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/stages_template.xlsx"
            fileDescription="File should contain columns: name, status"
          />
        </CardContent>
      </Card>

      {/* Add Stage Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Stage</DialogTitle>
            <DialogDescription>
              Create a new stage for customer workflow management.
            </DialogDescription>
          </DialogHeader>
          <StageForm
            onSubmit={handleCreateStage}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Stage Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Stage</DialogTitle>
            <DialogDescription>
              Update the stage information and settings.
            </DialogDescription>
          </DialogHeader>
          {selectedStage && (
            <StageForm
              defaultValues={selectedStage}
              onSubmit={handleUpdateStage}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Stage</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedStage?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
