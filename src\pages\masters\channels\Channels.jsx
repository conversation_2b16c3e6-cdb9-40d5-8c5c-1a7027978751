"use client";

import { useState, useEffect } from "react";
import { Plus, MoreHorizontal, Pencil, Trash2, Radio } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { ChannelForm } from "@/components/forms/channel-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import channelService from "../../../services/channels.service";
import mediumService from "../../../services/medium.service";

export default function Channels() {
  const { toast } = useToast();
  const [channels, setChannels] = useState([]);
  const [mediums, setMediums] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(null);

  // Fetch channels from API using the service
  const fetchChannels = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await channelService.getAllChannels({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const channelsArray = Array.isArray(result.channels)
        ? result.channels
        : [];
      setChannels(channelsArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch channels",
        variant: "destructive",
      });
      console.error("Error fetching channels:", error);
      setChannels([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch mediums for dropdown
  const fetchMediums = async () => {
    try {
      const result = await mediumService.getAllMedia();
      const mediumsArray = Array.isArray(result.media) ? result.media : [];
      setMediums(mediumsArray);
    } catch (error) {
      console.error("Error fetching mediums:", error);
    }
  };

  useEffect(() => {
    fetchChannels();
    fetchMediums();
  }, []);

  const handleAddChannel = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditChannel = (channel) => {
    setSelectedChannel(channel);
    setIsEditDialogOpen(true);
  };

  const handleDeleteChannel = (channel) => {
    setSelectedChannel(channel);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateChannel = async (data) => {
    try {
      await channelService.createChannel(data);
      await fetchChannels();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Channel created successfully",
      });
    } catch (error) {
      console.error("Create channel error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create channel",
        variant: "destructive",
      });
    }
  };

  const handleUpdateChannel = async (data) => {
    try {
      await channelService.updateChannel(selectedChannel.id, data);
      await fetchChannels();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Channel updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update channel",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteChannel = async () => {
    try {
      await channelService.deleteChannel(selectedChannel.id);
      await fetchChannels();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Channel deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete channel",
        variant: "destructive",
      });
    }
  };

  const handleImportChannels = async (file) => {
    try {
      const result = await channelService.bulkImportChannels(file);

      await fetchChannels(); // Refresh the channels list
      toast({
        title: "Success",
        description: "Channels imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import channels from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const getStatusBadge = (status) => {
    return status === "active" ? (
      <Badge className="bg-emerald-500">Active</Badge>
    ) : (
      <Badge variant="destructive">Inactive</Badge>
    );
  };

  const getMediumName = (mediumId) => {
    const medium = mediums.find((m) => m.id === mediumId);
    return medium ? medium.name : "Unknown";
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Channel Name",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <Radio className="h-4 w-4 text-muted-foreground" />
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "mediumId",
      header: "Medium",
      cell: ({ row }) => <div>{getMediumName(row.getValue("mediumId"))}</div>,
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: ({ row }) => {
        const description = row.getValue("description");
        return description ? (
          <div className="max-w-[200px] truncate">{description}</div>
        ) : (
          <div className="text-muted-foreground">No description</div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.getValue("status")),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const channel = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditChannel(channel)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteChannel(channel)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Channels Management</CardTitle>
              <CardDescription>
                Manage channels and their associated mediums
              </CardDescription>
            </div>
            <Button onClick={handleAddChannel}>
              <Plus className="mr-2 h-4 w-4" />
              Add Channel
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={channels}
            searchKey="name"
            isLoading={isLoading}
            entityName="Channels"
            onImport={handleImportChannels}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/channels_template.xlsx"
            fileDescription="File should contain columns: name, mediumId, description, status"
          />
        </CardContent>
      </Card>

      {/* Add Channel Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Channel</DialogTitle>
            <DialogDescription>
              Create a new channel and associate it with a medium.
            </DialogDescription>
          </DialogHeader>
          <ChannelForm
            mediums={mediums}
            onSubmit={handleCreateChannel}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Channel Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Channel</DialogTitle>
            <DialogDescription>
              Update the channel information and medium association.
            </DialogDescription>
          </DialogHeader>
          {selectedChannel && (
            <ChannelForm
              mediums={mediums}
              defaultValues={selectedChannel}
              onSubmit={handleUpdateChannel}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Channel Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedChannel?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteChannel}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
