import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class ZoneService {
  async getAllZones() {
    try {
      const response = await axios.get(`${API_URL}/zones`);
      return response.data;
    } catch (error) {
      console.error("Error fetching zones:", error);
      throw error;
    }
  }

  async getZoneById(id) {
    try {
      const response = await axios.get(`${API_URL}/zones/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching zone with ID ${id}:`, error);
      throw error;
    }
  }

  async createZone(zoneData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: zoneData.name,
        status: zoneData.status || "active", // Send status directly
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/zones`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating zone:", error);
      throw error;
    }
  }

  async updateZone(id, zoneData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: zoneData.name,
        status: zoneData.status || "active", // Send status directly
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/zones/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating zone with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteZone(id) {
    try {
      const response = await axios.delete(`${API_URL}/zones/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting zone with ID ${id}:`, error);
      throw error;
    }
  }

  async bulkImportZones(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/zones/insert-zones`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing zones from Excel:", error);
      throw error;
    }
  }

}

// Create and export a singleton instance
const zoneService = new ZoneService();
export default zoneService;
