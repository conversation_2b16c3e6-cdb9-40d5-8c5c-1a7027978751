import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

// Configure axios defaults if needed
// axios.defaults.headers.common['Authorization'] = `Bearer ${localStorage.getItem('token')}`;

class StateService {
  async getAllStates() {
    try {
      const response = await axios.get(`${API_URL}/states`);
      return response.data;
    } catch (error) {
      console.error("Error fetching states:", error);
      throw error;
    }
  }

  async getStateById(id) {
    try {
      const response = await axios.get(`${API_URL}/states/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching state with ID ${id}:`, error);
      throw error;
    }
  }

  async createState(stateData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: stateData.name,
        country: stateData.country || "India",
        status: stateData.status || "active", // Send status directly
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(`${API_URL}/states`, apiData);
      return response.data;
    } catch (error) {
      console.error("Error creating state:", error);
      throw error;
    }
  }

  async updateState(id, stateData) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        name: stateData.name,
        country: stateData.country || "India",
        status: stateData.status || "active", // Send status directly
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(`${API_URL}/states/${id}`, apiData);
      return response.data;
    } catch (error) {
      console.error(`Error updating state with ID ${id}:`, error);
      throw error;
    }
  }

  async deleteState(id) {
    try {
      const response = await axios.delete(`${API_URL}/states/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting state with ID ${id}:`, error);
      throw error;
    }
  }

  async bulkImportStates(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/states/insert-states`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Return the full response data which includes detailed results
      return response.data;
    } catch (error) {
      console.error("Error importing states from Excel:", error);
      throw error;
    }
  }

  getCountries() {
    return ["India", "USA", "UK", "Australia", "Canada"];
  }
}

// Create and export a singleton instance
const stateService = new StateService();
export default stateService;
