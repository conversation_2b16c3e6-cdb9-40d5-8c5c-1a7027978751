"use client";

import { useState, useEffect } from "react";
import { Plus, More<PERSON><PERSON><PERSON>tal, <PERSON><PERSON>l, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { MediumForm } from "@/components/forms/medium-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import mediumService from "../../../services/medium.service";

export default function Medium() {
  const { toast } = useToast();
  const [mediums, setMediums] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedMedium, setSelectedMedium] = useState(null);

  // Fetch media from API using the service
  const fetchMedia = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await mediumService.getAllMedia({ page, limit, search });

      // Ensure we have an array and handle potential data format issues
      const mediaArray = Array.isArray(result.media) ? result.media : [];
      setMediums(mediaArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch media",
        variant: "destructive",
      });
      console.error("Error fetching media:", error);
      setMediums([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMedia();
  }, []);

  const handleAddMedium = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditMedium = (medium) => {
    setSelectedMedium(medium);
    setIsEditDialogOpen(true);
  };

  const handleDeleteMedium = (medium) => {
    setSelectedMedium(medium);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateMedium = async (data) => {
    try {
      await mediumService.createMedium(data);
      await fetchMedia();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Medium created successfully",
      });
    } catch (error) {
      console.error("Create medium error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create medium",
        variant: "destructive",
      });
    }
  };

  const handleUpdateMedium = async (data) => {
    try {
      await mediumService.updateMedium(selectedMedium.id, data);
      await fetchMedia();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Medium updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update medium",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteMedium = async () => {
    try {
      await mediumService.deleteMedium(selectedMedium.id);
      await fetchMedia();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Medium deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete medium",
        variant: "destructive",
      });
    }
  };

  const handleImportMediums = async (file) => {
    try {
      const result = await mediumService.bulkImportMediums(file);

      await fetchMedia(); // Refresh the mediums list
      toast({
        title: "Success",
        description: "Mediums imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message || "Failed to import mediums from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Medium Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "description",
      header: "Description",
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("isActive");
        return (
          <Badge variant={isActive ? "success" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const medium = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditMedium(medium)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteMedium(medium)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Medium Management</CardTitle>
              <CardDescription>
                Manage media types and categories
              </CardDescription>
            </div>
            <Button onClick={handleAddMedium}>
              <Plus className="mr-2 h-4 w-4" />
              Add Medium
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={mediums}
            searchKey="name"
            isLoading={isLoading}
            entityName="Mediums"
            onImport={handleImportMediums}
            templatePath="/templates/mediums_template.xlsx"
            acceptedFileTypes=".xlsx,.xls,.csv"
            fileDescription="File should contain columns: name, description, status"
          />
        </CardContent>
      </Card>

      {/* Add Medium Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Medium</DialogTitle>
            <DialogDescription>
              Create a new medium type for your media categories.
            </DialogDescription>
          </DialogHeader>
          <MediumForm
            onSubmit={handleCreateMedium}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Medium Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Medium</DialogTitle>
            <DialogDescription>
              Update the medium information.
            </DialogDescription>
          </DialogHeader>
          {selectedMedium && (
            <MediumForm
              defaultValues={selectedMedium}
              onSubmit={handleUpdateMedium}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Medium Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedMedium?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteMedium}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
