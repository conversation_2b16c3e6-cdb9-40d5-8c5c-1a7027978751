"use client";

import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "./contexts/auth-context.jsx";
import { Toaster } from "./components/ui/toaster.jsx";

// Layout
import Layout from "./components/layout/Layout.jsx";

// Pages
import Dashboard from "./pages/dashboard/Dashboard.jsx";
import LoginPage from "./pages/login/Login.jsx";
import CustomersPage from "./pages/customers/Customers.jsx";
import CustomerDetailPage from "./pages/customers/CustomerDetail.jsx";
import AnalyticsPage from "./pages/analytics/Analytics.jsx";
import ReportsPage from "./pages/reports/Reports.jsx";
import SettingsPage from "./pages/settings/Settings.jsx";
import DocumentationPage from "./pages/documentation/Documentation.jsx";
import SystemStructurePage from "./pages/system-structure/SystemStructure.jsx";
import TransactionsPage from "./pages/transactions/Transactions.jsx";
import UsersPage from "./pages/users/Users.jsx";
import DatabaseSearchPage from "./pages/database-search/DatabaseSearch.jsx";
import CustomerJourneyPage from "./pages/customer-journey/CustomerJourney.jsx";
import AddCustomerJourneyPage from "./pages/customer-journey/AddCustomerJourney.jsx";
import EventsPage from "./pages/events/Events.jsx";
// import ConventionPage from "./pages/events/convention/Convention.jsx"
// import RevenuePage from "./pages/revenue/Revenue.jsx"
import MastersPage from "./pages/masters/Masters.jsx";
import VariantsPage from "./pages/masters/variants/Variants.jsx";
import MediumPage from "./pages/masters/medium/Medium.jsx";
import PropertiesPage from "./pages/masters/properties/Properties.jsx";
import CitiesPage from "./pages/masters/cities/Cities.jsx";
import StatePage from "./pages/masters/state/State.jsx";
import ZonePage from "./pages/masters/zone/Zone.jsx";
import StagesPage from "./pages/masters/stages/Stages.jsx";
import CompanyCategoriesPage from "./pages/masters/company-categories/CompanyCategories.jsx";
import CompaniesPage from "./pages/masters/companies/Companies.jsx";
import ChannelsPage from "./pages/masters/channels/Channels.jsx";
import ContactsPage from "./pages/masters/contacts/Contacts.jsx";
import CustomerEmployeesPage from "./pages/masters/customer-employees/CustomerEmployees.jsx";
import InsightsPage from "./pages/insights/Insights.jsx";
import TargetingPage from "./pages/targeting/Targeting.jsx";
import DemographicsPage from "./pages/demographics/Demographics.jsx";
import EventsReportingPage from "./pages/events-reporting/EventsReporting.jsx";
import ConventionEventsPage from "./pages/convention/Convention.jsx";
import AdvertisingPage from "./pages/advertising/Advertising.jsx";
import AwardsPage from "./pages/awards/Awards.jsx";
import RevenueTrackingPage from "./pages/revenue-tracking/RevenueTrackingPage.jsx";
import ContactStatuses from "./pages/masters/contact-statuses/ContactStatuses.jsx";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, isLoading } = useAuth();
  console.log("user", user);
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        Loading...
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return children;
};

function App() {
  return (
    <>
      <Routes>
        <Route path="/login" element={<LoginPage />} />

        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Dashboard />} />
          <Route path="companies" element={<CustomersPage />} />
          <Route path="reports" element={<ReportsPage />} />
          <Route path="customers/:id" element={<CustomerDetailPage />} />
          <Route path="analytics" element={<AnalyticsPage />} />
          <Route path="insights" element={<InsightsPage />} />
          <Route path="reports" element={<ReportsPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="documentation" element={<DocumentationPage />} />
          <Route path="targeting" element={<TargetingPage />} />
          <Route path="demographics" element={<DemographicsPage />} />
          <Route path="events-reporting" element={<EventsReportingPage />} />
          <Route path="system-structure" element={<SystemStructurePage />} />
          <Route path="convention" element={<ConventionEventsPage />} />
          <Route path="transactions" element={<TransactionsPage />} />
          <Route path="users" element={<UsersPage />} />
          <Route path="database-search" element={<DatabaseSearchPage />} />
          <Route path="advertising" element={<AdvertisingPage />} />
          <Route path="awards" element={<AwardsPage />} />
          <Route path="customer-journey" element={<CustomerJourneyPage />} />
          <Route
            path="customer-journey/add"
            element={<AddCustomerJourneyPage />}
          />
          <Route path="events" element={<EventsPage />} />

          {/* <Route path="revenue" element={<RevenuePage />} /> */}
          <Route path="revenue-tracking" element={<RevenueTrackingPage />} />
          <Route path="masters" element={<MastersPage />} />
          <Route path="masters/variants" element={<VariantsPage />} />
          <Route path="masters/medium" element={<MediumPage />} />
          <Route path="masters/properties" element={<PropertiesPage />} />
          <Route path="masters/cities" element={<CitiesPage />} />
          <Route path="masters/states" element={<StatePage />} />
          <Route path="masters/zones" element={<ZonePage />} />
          <Route path="masters/stages" element={<StagesPage />} />
          <Route
            path="masters/contact-statuses"
            element={<ContactStatuses />}
          />
          <Route
            path="masters/company-categories"
            element={<CompanyCategoriesPage />}
          />
          <Route path="masters/company" element={<CompaniesPage />} />
          <Route path="masters/channels" element={<ChannelsPage />} />
          <Route path="masters/contacts" element={<ContactsPage />} />
          <Route
            path="masters/customer-employees"
            element={<CustomerEmployeesPage />}
          />
          <Route path="masters/users" element={<UsersPage />} />
        </Route>
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
