"use client";

import { useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Search } from "lucide-react";

import { Button } from "./ui/button.jsx";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu.jsx";
import { Input } from "./ui/input.jsx";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table.jsx";
import { DataImportExport } from "./data-import-export.jsx";

export function DataTable({
  columns,
  data,
  searchKey, // Single search key (for backward compatibility)
  searchKeys = [], // New prop for multiple search keys
  entityName,
  onImport,
  onExport,
  onAdd,
  addButtonLabel = "Add New",
  acceptedFileTypes,
  templatePath,
  fileDescription,
  isImportExportSectionVisible = true,
}) {
  const [sorting, setSorting] = useState([]);
  const [columnFilters, setColumnFilters] = useState([]);
  const [columnVisibility, setColumnVisibility] = useState({});
  const [rowSelection, setRowSelection] = useState({});
  const [globalFilter, setGlobalFilter] = useState("");

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: (row, columnId, filterValue) => {
      // If searchKeys array is provided, search across all specified keys
      if (searchKeys.length > 0) {
        return searchKeys.some((key) => {
          const value = row.getValue(key);
          if (!value) return false;

          // Handle objects with name property (like customerInfo.name)
          if (typeof value === "object" && value !== null && value.name) {
            return value.name.toLowerCase().includes(filterValue.toLowerCase());
          }

          // Handle string values
          if (typeof value === "string") {
            return value.toLowerCase().includes(filterValue.toLowerCase());
          }

          return false;
        });
      }

      // If only searchKey is provided (backward compatibility)
      if (searchKey) {
        const value = row.getValue(searchKey);
        if (!value) return false;

        // Handle objects with name property
        if (typeof value === "object" && value !== null && value.name) {
          return value.name.toLowerCase().includes(filterValue.toLowerCase());
        }

        // Handle string values
        if (typeof value === "string") {
          return value.toLowerCase().includes(filterValue.toLowerCase());
        }

        return false;
      }

      // Default: search all string fields
      return Object.values(row.original).some((value) => {
        if (typeof value === "string") {
          return value.toLowerCase().includes(filterValue.toLowerCase());
        }

        // Handle objects with name property
        if (typeof value === "object" && value !== null && value.name) {
          return value.name.toLowerCase().includes(filterValue.toLowerCase());
        }

        return false;
      });
    },
  });

  function exportTableVisibleText(table, columns) {
    const rows = table.getRowModel().rows;

    const headers = columns
      .filter((col) => table.getColumn(col.accessorKey)?.getIsVisible())
      .map((col) =>
        typeof col.header === "string" ? col.header : col.accessorKey
      );

    const dataRows = rows.map((row) => {
      return columns
        .filter((col) => table.getColumn(col.accessorKey)?.getIsVisible())
        .map((col) => {
          if (col.cell) {
            // simulate the cell rendering with your row data
            const rendered = col.cell({ row });
            if (typeof rendered === "string" || typeof rendered === "number") {
              return rendered;
            }

            // If it's a React element like <div>Text</div>, extract children
            if (rendered?.props?.children) {
              const children = rendered.props.children;
              if (
                typeof children === "string" ||
                typeof children === "number"
              ) {
                return children;
              }
              if (Array.isArray(children)) {
                return children
                  .map((child) =>
                    typeof child === "string" || typeof child === "number"
                      ? child
                      : child?.props?.children || ""
                  )
                  .join("");
              }
              return children.toString();
            }
          }

          // fallback: get raw value
          return row.getValue(col.accessorKey);
        });
    });

    const csv = [headers.join(","), ...dataRows.map((r) => r.join(","))].join(
      "\n"
    );

    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${entityName || "exported-data"}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row items-center py-4 gap-3">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search..."
            value={globalFilter}
            onChange={(event) => setGlobalFilter(event.target.value)}
            className="pl-8 w-full md:max-w-sm"
          />
        </div>

        <div className="flex items-center gap-2 ml-auto">
          {isImportExportSectionVisible && (
            <DataImportExport
              entityName={entityName}
              onImport={onImport}
              onExport={() => exportTableVisibleText(table, columns)}
              acceptedFileTypes={acceptedFileTypes}
              templatePath={templatePath}
              fileDescription={fileDescription}
            />
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {onAdd && <Button onClick={onAdd}>{addButtonLabel}</Button>}
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder ? null : (
                      <button
                        onClick={header.column.getToggleSortingHandler()}
                        className="flex items-center gap-1 cursor-pointer select-none"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {{
                          asc: "↑",
                          desc: "↓",
                        }[header.column.getIsSorted()] ?? null}
                      </button>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
