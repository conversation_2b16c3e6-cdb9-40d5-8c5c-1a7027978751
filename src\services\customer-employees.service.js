import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:4000/api";

class CustomerEmployeeService {
  async getAllCustomerEmployees(params = {}) {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page);
      if (params.limit) queryParams.append("limit", params.limit);
      if (params.search) queryParams.append("search", params.search);
      if (params.customerId)
        queryParams.append("customerId", params.customerId);
      if (params.status) queryParams.append("status", params.status);

      const queryString = queryParams.toString();
      const url = queryString
        ? `${API_URL}/customer-employees?${queryString}`
        : `${API_URL}/customer-employees`;

      const response = await axios.get(url);

      // Ensure data is in expected format
      const employeesData = Array.isArray(response.data) ? response.data : [];

      return {
        customerEmployees: employeesData,
      };
    } catch (error) {
      console.error("Error fetching customer employees:", error);
      throw error;
    }
  }

  async getCustomerEmployeeById(id) {
    try {
      const response = await axios.get(`${API_URL}/customer-employees/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching customer employee:", error);
      throw error;
    }
  }

  async createCustomerEmployee(data) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        customerId: parseInt(data.customerId),
        name: data.name,
        email: data.email || null,
        phone: data.phone || null,
        designation: data.designation || null,
        department: data.department || null,
        isPrimary: data.isPrimary || false,
        status: data.status || "active",
        contactStatusId: parseInt(data.contactStatusId),
        notes: data.notes || null,
        // createdBy will be handled by backend from auth context
      };

      const response = await axios.post(
        `${API_URL}/customer-employees`,
        apiData
      );
      return response.data;
    } catch (error) {
      console.error("Error creating customer employee:", error);
      throw error;
    }
  }

  async updateCustomerEmployee(id, data) {
    try {
      // Transform form data to match backend model expectations
      const apiData = {
        customerId: parseInt(data.customerId),
        name: data.name,
        email: data.email || null,
        phone: data.phone || null,
        designation: data.designation || null,
        department: data.department || null,
        isPrimary: data.isPrimary || false,
        status: data.status || "active",
        contactStatusId: parseInt(data.contactStatusId),
        notes: data.notes || null,
        // updatedBy will be handled by backend from auth context
      };

      const response = await axios.put(
        `${API_URL}/customer-employees/${id}`,
        apiData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating customer employee:", error);
      throw error;
    }
  }

  async deleteCustomerEmployee(id) {
    try {
      const response = await axios.delete(
        `${API_URL}/customer-employees/${id}`
      );
      return response.data;
    } catch (error) {
      console.error("Error deleting customer employee:", error);
      throw error;
    }
  }

  async bulkImportCustomerEmployees(file) {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${API_URL}/customer-employees/bulk-import`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error importing customer employees:", error);
      throw error;
    }
  }
}

export default new CustomerEmployeeService();
