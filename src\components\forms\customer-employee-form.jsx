"use client";

import { useState, useEffect } from "react";
import { useForm, Controller } from "react-hook-form";
import { But<PERSON> } from "../ui/button.jsx";
import { Input } from "../ui/input.jsx";
import { Label } from "../ui/label.jsx";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select.jsx";
import { Textarea } from "../ui/textarea.jsx";
import { Checkbox } from "../ui/checkbox.jsx";
import customerService from "../../services/customer.service.js";
import contactStatusService from "../../services/contact-statuses.service.js";

const statusOptions = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

export function CustomerEmployeeForm({ defaultValues, onSubmit, onCancel }) {
  const [customers, setCustomers] = useState([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState(false);
  const [contactStatuses, setContactStatuses] = useState([]);
  const [isLoadingContactStatuses, setIsLoadingContactStatuses] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    watch,
  } = useForm({
    defaultValues: defaultValues ? {
      customerId: defaultValues.customerId ? defaultValues.customerId.toString() : "",
      name: defaultValues.name || "",
      email: defaultValues.email || "",
      phone: defaultValues.phone || "",
      designation: defaultValues.designation || "",
      department: defaultValues.department || "",
      isPrimary: defaultValues.isPrimary || false,
      status: defaultValues.status || "active",
      contactStatusId: defaultValues.contactStatusId ? defaultValues.contactStatusId.toString() : "",
      notes: defaultValues.notes || "",
    } : {
      customerId: "",
      name: "",
      email: "",
      phone: "",
      designation: "",
      department: "",
      isPrimary: false,
      status: "active",
      contactStatusId: "",
      notes: "",
    },
  });

  // Load customers for dropdown
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        setIsLoadingCustomers(true);
        const result = await customerService.getAllCustomers({ limit: 1000 });
        setCustomers(result.customers || []);
      } catch (error) {
        console.error("Error loading customers:", error);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    const loadContactStatuses = async () => {
      try {
        setIsLoadingContactStatuses(true);
        const result = await contactStatusService.getAllContactStatuses({ limit: 1000 });
        setContactStatuses(result.contactStatuses || []);
      } catch (error) {
        console.error("Error loading contact statuses:", error);
      } finally {
        setIsLoadingContactStatuses(false);
      }
    };

    loadCustomers();
    loadContactStatuses();
  }, []);

  const handleFormSubmit = async (data) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  return (
    <div className="max-h-[70vh] overflow-y-auto pr-2">
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="customerId">Company</Label>
            <Controller
              name="customerId"
              control={control}
              render={({ field }) => (
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Company" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingCustomers ? (
                      <SelectItem value="loading" disabled>
                        Loading contacts...
                      </SelectItem>
                    ) : customers.length === 0 ? (
                      <SelectItem value="no-customers" disabled>
                        No contacts available
                      </SelectItem>
                    ) : (
                      customers.map((customer) => (
                        <SelectItem
                          key={customer.id}
                          value={customer.id.toString()}
                        >
                          {customer.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.customerId && (
              <p className="text-sm text-red-500">
                {errors.customerId.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              {...register("name", { required: "Name is required" })}
              placeholder="Enter  name"
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register("email", {
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              placeholder="Enter email address"
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              {...register("phone")}
              placeholder="Enter phone number"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="designation">Designation</Label>
            <Input
              id="designation"
              {...register("designation")}
              placeholder="Enter designation"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Input
              id="department"
              {...register("department")}
              placeholder="Enter department"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactStatusId">Contact Status</Label>
            <Controller
              name="contactStatusId"
              control={control}
              render={({ field }) => (
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Contact Status" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingContactStatuses ? (
                      <SelectItem value="loading" disabled>
                        Loading statuses...
                      </SelectItem>
                    ) : contactStatuses.length === 0 ? (
                      <SelectItem value="no-statuses" disabled>
                        No statuses available
                      </SelectItem>
                    ) : (
                      contactStatuses.map((status) => (
                        <SelectItem
                          key={status.id}
                          value={status.id.toString()}
                        >
                          {status.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.contactStatusId && (
              <p className="text-sm text-red-500">
                {errors.contactStatusId.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            {...register("notes")}
            placeholder="Add any additional notes"
            rows="3"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Controller
            name="isPrimary"
            control={control}
            render={({ field }) => (
              <Checkbox
                id="isPrimary"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <Label htmlFor="isPrimary">Primary Contact</Label>
        </div>

        <div className="flex justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Save Contact"}
          </Button>
        </div>
      </form>
    </div>
  );
}
