import React, { useState, useEffect } from "react";
import {
  <PERSON>DownRight,
  ArrowUpRight,
  Calendar,
  ChevronDown,
  Download,
  Filter,
  HelpCircle,
  Info,
  Search,
} from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";

// Note: Adjust these import paths based on your project structure.
// These components are likely from a UI library like shadcn/ui.
import { Button } from "../../components/ui/button";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Input } from "../../components/ui/input";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../../components/ui/chart";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import { MobileHeader } from "../../components/mobile-header";
import dashboardService from "../../services/dashboard.service";

// Analytics data will be fetched from API

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  }).format(date);
};

// Format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    currencyDisplay: "symbol",
  }).format(amount);
};

const AnalyticsPage = () => {
  const [selectedMetric, setSelectedMetric] = useState("revenue");
  const [selectedTab, setSelectedTab] = useState("overview");
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    year: "2025",
    month: "all",
    view: "overview"
  });

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await dashboardService.getAnalyticsData(filters);
      setAnalyticsData(response.data);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [filters]);

  // Calculate totals and comparisons from real data
  const currentPeriodTotal = analyticsData?.salesDataByYear?.data?.reduce((acc, item) => {
    return {
      revenue: acc.revenue + (item.revenue || 0),
      orders: acc.orders + (item.channels || 0),
      mediums: acc.mediums + (item.mediums || 0),
      customers: acc.customers + (item.customers || 0),
    };
  }, { revenue: 0, orders: 0, mediums: 0, customers: 0 }) || {
    revenue: 0,
    orders: 0,
    mediums: 0,
    customers: 0,
  };

  const previousPeriodTotal = {
    revenue: currentPeriodTotal.revenue * 0.9, // Simulate previous period
    orders: currentPeriodTotal.orders * 0.9,
    mediums: currentPeriodTotal.mediums * 0.9,
    customers: currentPeriodTotal.customers * 0.9,
  };

  const getPercentageChange = (current, previous) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const percentageChanges = {
    revenue: getPercentageChange(
      currentPeriodTotal.revenue,
      previousPeriodTotal.revenue
    ),
    orders: getPercentageChange(
      currentPeriodTotal.orders,
      previousPeriodTotal.orders
    ),
    mediums: getPercentageChange(
      currentPeriodTotal.mediums,
      previousPeriodTotal.mediums
    ),
    customers: getPercentageChange(
      currentPeriodTotal.customers,
      previousPeriodTotal.customers
    ),
  };

  // Transform API data for charts
  const salesData = analyticsData?.salesDataByYear?.data || [];
  const channelData = analyticsData?.topChannelsByRevenue?.data?.map(channel => ({
    name: channel.channelName,
    value: channel.totalRevenue
  })) || [];
  const topCustomers = analyticsData?.topCustomersByRevenue?.data?.map(customer => ({
    id: customer.customerId,
    name: customer.customerName,
    totalSpent: customer.totalRevenue,
    orders: customer.transactionCount,
    lastPurchase: "2023-11-15", // This would need to come from API
  })) || [];
  const dailyRevenueData = analyticsData?.dailyRevenueLast7Days?.data || [];
  const monthlyChannelData = analyticsData?.monthlyChannelRevenue?.data || [];
  const allChannelsData = analyticsData?.allChannelsRevenue?.data || [];

  // Get dynamic channel keys from monthly data
  const getChannelKeys = () => {
    if (!monthlyChannelData || monthlyChannelData.length === 0) return [];
    
    // Get all unique keys except 'month'
    const allKeys = new Set();
    monthlyChannelData.forEach(item => {
      Object.keys(item).forEach(key => {
        if (key !== 'month') {
          allKeys.add(key);
        }
      });
    });
    
    return Array.from(allKeys);
  };

  const channelKeys = getChannelKeys();

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <MobileHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Loading analytics data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <MobileHeader />
      <div className="border-b hidden md:block">
        <div className="flex min-h-[64px] items-center px-4 gap-4 py-3">
          <h1 className="text-lg font-semibold">Analytics</h1>
          <div className="ml-auto flex items-center gap-4 flex-wrap">
            <div className="relative z-10">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search analytics..."
                className="w-[200px] lg:w-[300px] pl-8"
              />
            </div>

            <div className="ml-auto flex items-center gap-4 flex-wrap">
              <Select value={filters.year} onValueChange={(value) => setFilters(prev => ({ ...prev, year: value }))}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2025">2025</SelectItem>
                  <SelectItem value="2024">2024</SelectItem>
                  <SelectItem value="2023">2023</SelectItem>
                  <SelectItem value="2022">2022</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.month} onValueChange={(value) => setFilters(prev => ({ ...prev, month: value }))}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Months</SelectItem>
                  <SelectItem value="jan">January</SelectItem>
                  <SelectItem value="feb">February</SelectItem>
                  <SelectItem value="mar">March</SelectItem>
                  <SelectItem value="apr">April</SelectItem>
                  <SelectItem value="may">May</SelectItem>
                  <SelectItem value="jun">June</SelectItem>
                  <SelectItem value="jul">July</SelectItem>
                  <SelectItem value="aug">August</SelectItem>
                  <SelectItem value="sep">September</SelectItem>
                  <SelectItem value="oct">October</SelectItem>
                  <SelectItem value="nov">November</SelectItem>
                  <SelectItem value="dec">December</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.view} onValueChange={(value) => setFilters(prev => ({ ...prev, view: value }))}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="View" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview</SelectItem>
                  <SelectItem value="by-medium">By Medium</SelectItem>
                  <SelectItem value="by-channel">By Channel</SelectItem>
                  <SelectItem value="by-property">By Property</SelectItem>
                  <SelectItem value="by-contact">
                    By Contact Category
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        {/* Key metrics */}
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Revenue
              </CardTitle>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-4 w-4">
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">About this metric</h4>
                    <p className="text-sm text-muted-foreground">
                      Total revenue represents the sum of all sales during the
                      selected period.
                    </p>
                  </div>
                </PopoverContent>
              </Popover>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(currentPeriodTotal.revenue)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {percentageChanges.revenue >= 0 ? (
                  <>
                    <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                    <span className="text-emerald-500 font-medium">
                      +{percentageChanges.revenue.toFixed(1)}%
                    </span>
                  </>
                ) : (
                  <>
                    <ArrowDownRight className="mr-1 h-4 w-4 text-rose-500" />
                    <span className="text-rose-500 font-medium">
                      {percentageChanges.revenue.toFixed(1)}%
                    </span>
                  </>
                )}
                <span className="ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Customers
              </CardTitle>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-4 w-4">
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">About this metric</h4>
                    <p className="text-sm text-muted-foreground">
                      Total customers
                    </p>
                  </div>
                </PopoverContent>
              </Popover>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(currentPeriodTotal.revenue)}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                {percentageChanges.revenue >= 0 ? (
                  <>
                    <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
                    <span className="text-emerald-500 font-medium">
                      +{percentageChanges.revenue.toFixed(1)}%
                    </span>
                  </>
                ) : (
                  <>
                    <ArrowDownRight className="mr-1 h-4 w-4 text-rose-500" />
                    <span className="text-rose-500 font-medium">
                      {percentageChanges.revenue.toFixed(1)}%
                    </span>
                  </>
                )}
                <span className="ml-1">vs. previous period</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main chart section */}
        <Card>
          <CardHeader className="flex flex-row items-center flex-wrap gap-2">
            <div>
              <CardTitle>Performance Over Time</CardTitle>
              <CardDescription>
                View trends for key metrics over the selected time period
              </CardDescription>
            </div>
            <div className="ml-auto flex items-center gap-2 flex-wrap">
              <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="channels">Channel</SelectItem>
                  <SelectItem value="mediums">Media</SelectItem>
                  <SelectItem value="customers">Customers</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="px-2">
            <div className="h-[350px] w-full overflow-x-auto">
              <ChartContainer
                config={{
                  [selectedMetric]: {
                    label:
                      selectedMetric === "revenue"
                        ? "Revenue"
                        : selectedMetric === "channels"
                        ? "Channels"
                        : selectedMetric === "mediums"
                        ? "Mediums"
                        : "Customers",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-full min-w-[600px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={salesData}
                    margin={{ top: 10, right: 30, left: 10, bottom: 10 }}
                  >
                    <defs>
                      <linearGradient
                        id="colorMetric"
                        x1="0"
                        y1="0"
                        x2="0"
                        y2="1"
                      >
                        <stop
                          offset="5%"
                          stopColor="var(--color-revenue)"
                          stopOpacity={0.8}
                        />
                        <stop
                          offset="95%"
                          stopColor="var(--color-revenue)"
                          stopOpacity={0.1}
                        />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(date) => {
                        const d = new Date(date);
                        return `${d.toLocaleString("default", {
                          month: "short",
                        })} ${d.getDate()}`;
                      }}
                      padding={{ left: 20, right: 20 }}
                    />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area
                      type="monotone"
                      dataKey={selectedMetric}
                      stroke="var(--color-revenue)"
                      fillOpacity={1}
                      fill="url(#colorMetric)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between text-xs text-muted-foreground">
            <div>Updated: Today at 2:30 PM</div>
            <Button variant="link" size="sm" className="text-xs">
              View detailed report
            </Button>
          </CardFooter>
        </Card>

        {/* Tabs for different analytics views */}
        <Tabs
          defaultValue="overview"
          className="space-y-4"
          onValueChange={setSelectedTab}
        >
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-2 lg:w-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="channels">Channels</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Sales by Channel</CardTitle>
                  <CardDescription>
                    Distribution of sales across different channels
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-2">
                  <div className="h-[300px] w-full overflow-x-auto">
                    <ChartContainer
                      config={{
                        value: {
                          label: "Revenue",
                          color: "hsl(var(--chart-3))",
                        },
                      }}
                      className="h-full min-w-[400px]"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={channelData}
                          layout="vertical"
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            horizontal={true}
                            vertical={false}
                          />
                          <XAxis type="number" />
                          <YAxis dataKey="name" type="category" width={100} />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Bar
                            dataKey="value"
                            fill="var(--color-value)"
                            radius={[0, 4, 4, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Performance</CardTitle>
                  <CardDescription>
                    Daily revenue for the last 7 days
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-2">
                  <div className="h-[300px] w-full overflow-x-auto">
                    <ChartContainer
                      config={{
                        revenue: {
                          label: "Revenue",
                          color: "hsl(var(--chart-2))",
                        },
                      }}
                      className="h-full min-w-[400px]"
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={dailyRevenueData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis
                            dataKey="label"
                          />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Bar
                            dataKey="revenue"
                            fill="var(--color-revenue)"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Top Customers</CardTitle>
                    <CardDescription>
                      Customers with highest spend
                    </CardDescription>
                  </div>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </CardHeader>
                <CardContent className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Customer</TableHead>
                        <TableHead className="text-right">
                          Total Spent
                        </TableHead>
                        <TableHead className="text-right">Orders</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {topCustomers.slice(0, 5).map((customer) => (
                        <TableRow key={customer.id}>
                          <TableCell className="font-medium">
                            {customer.name}
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(customer.totalSpent)}
                          </TableCell>
                          <TableCell className="text-right">
                            {customer.orders}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Channels Tab */}
          <TabsContent value="channels" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue by Channel</CardTitle>
                  <CardDescription>
                    Distribution of revenue across channels
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-2">
                  <div className="h-[300px]">
                    <ChartContainer
                      config={{
                        value: {
                          label: "Revenue",
                          color: "hsl(var(--chart-1))",
                        },
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={allChannelsData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Bar
                            dataKey="value"
                            fill="var(--color-value)"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card>

              {/* <Card>
                <CardHeader>
                  <CardTitle>Channel Growth</CardTitle>
                  <CardDescription>
                    Year-over-year growth by channel
                  </CardDescription>
                </CardHeader>
                <CardContent className="px-2">
                  <div className="h-[300px]">
                    <ChartContainer
                      config={{
                        growth: {
                          label: "Growth %",
                          color: "hsl(var(--chart-2))",
                        },
                      }}
                    >
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: "Print", growth: 5 },
                            { name: "Digital", growth: 25 },
                            { name: "Events", growth: 15 },
                            { name: "Sponsorships", growth: 18 },
                            { name: "Awards", growth: 22 },
                          ]}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            vertical={false}
                          />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <ChartTooltip content={<ChartTooltipContent />} />
                          <Bar
                            dataKey="growth"
                            fill="var(--color-growth)"
                            radius={[4, 4, 0, 0]}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </ChartContainer>
                  </div>
                </CardContent>
              </Card> */}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Channel Performance Over Time</CardTitle>
                <CardDescription>Monthly revenue by channel</CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div>
                  <ChartContainer
                    config={channelKeys.reduce((acc, key, index) => {
                      acc[key] = {
                        label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
                        color: `hsl(var(--chart-${(index % 3) + 1}))`,
                      };
                      return acc;
                    }, {})}
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={monthlyChannelData}
                        margin={{ top: 10, right: 30, left: 10, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        {channelKeys.map((key, index) => (
                          <Area
                            key={key}
                            type="monotone"
                            dataKey={key}
                            stackId="1"
                            stroke={`var(--color-${key})`}
                            fill={`var(--color-${key})`}
                          />
                        ))}
                      </AreaChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Channel Details</CardTitle>
                  <CardDescription>
                    Performance metrics by channel
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Channel</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                      <TableHead className="text-right">Orders</TableHead>
                      <TableHead className="text-right">
                        Avg. Order Value
                      </TableHead>
                      <TableHead className="text-right">YoY Growth</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analyticsData?.topChannelsByRevenue?.data?.map((channel) => (
                      <TableRow key={channel.channelId}>
                        <TableCell className="font-medium">{channel.channelName}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(channel.totalRevenue)}
                        </TableCell>
                        <TableCell className="text-right">{channel.transactionCount}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(channel.totalRevenue / channel.transactionCount)}
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="text-emerald-500">+15%</span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsPage;
