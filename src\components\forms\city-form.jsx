"use client";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import stateService from "../../services/states.service";
import { COUNTRIES } from "../../constants";
import { useEffect, useState } from "react";

export function CityForm({ onSubmit, onCancel, defaultValues = {} }) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      status: "active",
      ...defaultValues,
    },
  });

  const status = watch("status");

  const handleFormSubmit = (data) => {
    onSubmit(data);
  };
  const [states, setStates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchStates = async () => {
    try {
      setIsLoading(true);
      const result = await stateService.getAllStates();
      const stateValues = result.map((state) => state.name);
      setStates(stateValues || []);
    } catch (error) {
      console.error("Error fetching states:", error);
      setStates([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStates();
  }, []);

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">City Name *</Label>
        <Input
          id="name"
          {...register("name", { required: "City name is required" })}
          placeholder="Enter city name"
        />
        {errors.name && (
          <p className="text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="state">State *</Label>
        <Select
          value={watch("state") || ""}
          onValueChange={(value) => setValue("state", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select state" />
          </SelectTrigger>
          <SelectContent>
            {states.map((state) => (
              <SelectItem key={state} value={state}>
                {state}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="country">Country *</Label>
        <Select
          value={watch("country") || ""}
          onValueChange={(value) => setValue("country", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select country" />
          </SelectTrigger>
          <SelectContent>
            {COUNTRIES.map((country) => (
              <SelectItem key={country} value={country}>
                {country}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="status"
          checked={status === "active"}
          onCheckedChange={(checked) =>
            setValue("status", checked ? "active" : "inactive")
          }
        />
        <Label htmlFor="status">Active</Label>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save City</Button>
      </div>
    </form>
  );
}
