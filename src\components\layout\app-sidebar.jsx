// Converted from Next.js to React (React Router DOM based)

import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../../contexts/auth-context.jsx";
import {
  BarChart3,
  Users,
  TrendingUp,
  Settings,
  LogOut,
  FileText,
  CreditCard,
  Book,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar.jsx";

import { Sheet, SheetContent, SheetTrigger } from "../ui/sheet.jsx";
import { ScrollArea } from "../ui/scroll-area.jsx";
import { MobileHeader } from "../mobile-header.jsx";
import { Button } from "../ui/button.jsx";
import { cn } from "../../lib/utils.js";

const menuItems = [
  {
    title: "Dashboard",
    href: "/",
    icon: <BarChart3 className="h-5 w-5" />,
  },
  //   {
  //     title: "Analytics",
  //     href: "/analytics",
  //     icon: <TrendingUp className="h-5 w-5" />,
  //   },
  {
    title: "Reports",
    href: "/reports",
    icon: <Book className="h-5 w-5" />,
  },

  {
    title: "Transactions",
    href: "/transactions",
    icon: <CreditCard className="h-5 w-5" />,
  },
  {
    title: "Data Management",
    href: "/masters",
    icon: <CreditCard className="h-5 w-5" />,
    submenu: [
      {
        title: "All Masters",
        href: "/masters",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Contacts",
        href: "/masters/customer-employees",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Companies",
        href: "/companies",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Contact Statuses",
        href: "/masters/contact-statuses",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Cities",
        href: "/masters/cities",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Medium",
        href: "/masters/medium",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Channels",
        href: "/masters/channels",
        icon: <Users className="h-5 w-5" />,
      },
      // {
      //   title: "Contacts",
      //   href: "/masters/contacts",
      //   icon: <Users className="h-5 w-5" />,
      // },

      {
        title: "Company Categories",
        href: "/masters/company-categories",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Stages",
        href: "/masters/stages",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Properties",
        href: "/masters/properties",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Variants",
        href: "/masters/variants",
        icon: <Users className="h-5 w-5" />,
      },
      {
        title: "Users",
        href: "/masters/users",
        icon: <Users className="h-5 w-5" />,
      },
    ],
  },
  //   {
  //     title: "System",
  //     href: "/system-structure",
  //     icon: <Settings className="h-5 w-5" />,
  //     submenu: [
  //       {
  //         title: "Documentation",
  //         href: "/documentation",
  //         icon: <FileText className="h-5 w-5" />,
  //       },
  //       {
  //         title: "System Structure",
  //         href: "/system-structure",
  //         icon: <FileText className="h-5 w-5" />,
  //       },
  //       {
  //         title: "Settings",
  //         href: "/settings",
  //         icon: <FileText className="h-5 w-5" />,
  //       },
  //     ],
  //   },
];

export function AppSidebar() {
  const location = useLocation();
  const { logout } = useAuth();
  const pathname = location.pathname;

  return (
    <>
      <Sheet>
        <SheetTrigger asChild>
          <MobileHeader />
        </SheetTrigger>
        <SheetContent side="left" className="w-72 p-0">
          <Sidebar className="border-r-0">
            <ScrollArea className="h-[calc(100vh-3.5rem)]">
              <div className="px-3 py-2">
                <h2 className="mb-2 px-4 text-lg font-semibold">Navigation</h2>
                <div className="space-y-1 mt-4">
                  {renderNavItems(menuItems, pathname)}
                </div>
              </div>
            </ScrollArea>
            <div className="mt-auto p-4">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={logout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </Sidebar>
        </SheetContent>
      </Sheet>

      <Sidebar className="hidden border-r lg:block">
        <ScrollArea className="h-[calc(100vh-3.5rem)]">
          <div className="px-3 py-2">
            <h2 className="mb-2 px-4 text-lg font-semibold">Navigation</h2>
            <div className="space-y-1 mt-6">
              {renderNavItems(menuItems, pathname)}
            </div>
          </div>
        </ScrollArea>
        <div className="mt-auto p-4">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={logout}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Logout
          </Button>
        </div>
      </Sidebar>
    </>
  );
}

function renderNavItems(items, pathname) {
  return items.map((item, index) => {
    const isActive = pathname === item.href;

    if (item.submenu) {
      return (
        <div key={index} className="space-y-1" style={{ marginTop: "2rem" }}>
          <h3 className="px-4 py-2 text-sm font-medium">{item.title}</h3>
          <div className="space-y-1 pl-4">
            {renderNavItems(item.submenu, pathname)}
          </div>
        </div>
      );
    }

    return (
      <Link
        key={index}
        to={item.href}
        className={cn(
          "flex items-center gap-3 rounded-md px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
          isActive ? "bg-accent text-accent-foreground" : "transparent"
        )}
      >
        {item.icon}
        {item.title}
      </Link>
    );
  });
}
