"use client";

import { useState, useEffect } from "react";
import { Plus, MoreHorizontal, Pencil, Trash2, Building } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { CompanyCategoryForm } from "@/components/forms/company-category-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import companyCategoryService from "../../../services/company-categories.service";

export default function CompanyCategories() {
  const { toast } = useToast();
  const [companyCategories, setCompanyCategories] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCompanyCategory, setSelectedCompanyCategory] = useState(null);

  // Fetch company categories from API using the service
  const fetchCompanyCategories = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await companyCategoryService.getAllCompanyCategories({ page, limit, search });

      // Ensure we have an array and handle potential data format issues
      const companyCategoriesArray = Array.isArray(result.companyCategories) ? result.companyCategories : [];
      setCompanyCategories(companyCategoriesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch company categories",
        variant: "destructive",
      });
      console.error("Error fetching company categories:", error);
      setCompanyCategories([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanyCategories();
  }, []);

  const handleAddCompanyCategory = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditCompanyCategory = (companyCategory) => {
    setSelectedCompanyCategory(companyCategory);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCompanyCategory = (companyCategory) => {
    setSelectedCompanyCategory(companyCategory);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateCompanyCategory = async (companyCategoryData) => {
    try {
      await companyCategoryService.createCompanyCategory(companyCategoryData);
      toast({
        title: "Success",
        description: "Company category created successfully",
      });
      setIsAddDialogOpen(false);
      fetchCompanyCategories(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create company category",
        variant: "destructive",
      });
      console.error("Error creating company category:", error);
    }
  };

  const handleUpdateCompanyCategory = async (companyCategoryData) => {
    try {
      await companyCategoryService.updateCompanyCategory(selectedCompanyCategory.id, companyCategoryData);
      toast({
        title: "Success",
        description: "Company category updated successfully",
      });
      setIsEditDialogOpen(false);
      setSelectedCompanyCategory(null);
      fetchCompanyCategories(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update company category",
        variant: "destructive",
      });
      console.error("Error updating company category:", error);
    }
  };

  const handleConfirmDelete = async () => {
    try {
      await companyCategoryService.deleteCompanyCategory(selectedCompanyCategory.id);
      toast({
        title: "Success",
        description: "Company category deleted successfully",
      });
      setIsDeleteDialogOpen(false);
      setSelectedCompanyCategory(null);
      fetchCompanyCategories(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete company category",
        variant: "destructive",
      });
      console.error("Error deleting company category:", error);
    }
  };

  const handleImportCompanyCategories = async (file) => {
    try {
      await companyCategoryService.importCompanyCategories(file);
      toast({
        title: "Success",
        description: "Company categories imported successfully",
      });
      fetchCompanyCategories(); // Refresh the list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to import company categories",
        variant: "destructive",
      });
      console.error("Error importing company categories:", error);
    }
  };

  // Define table columns
  const columns = [
    {
      accessorKey: "name",
      header: "Company Category Name",
      cell: ({ row }) => (
        <div className="flex items-center">
          <Building className="mr-2 h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue("name")}</span>
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "active" ? "default" : "secondary"}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("createdAt");
        return date ? new Date(date).toLocaleDateString() : "-";
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const companyCategory = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditCompanyCategory(companyCategory)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteCompanyCategory(companyCategory)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Company Categories Management</CardTitle>
              <CardDescription>
                Manage company categories and business classifications
              </CardDescription>
            </div>
            <Button onClick={handleAddCompanyCategory}>
              <Plus className="mr-2 h-4 w-4" />
              Add Company Category
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={companyCategories}
            searchKey="name"
            isLoading={isLoading}
            entityName="Company Categories"
            onImport={handleImportCompanyCategories}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/company_categories_template.xlsx"
            fileDescription="File should contain columns: name, status"
          />
        </CardContent>
      </Card>

      {/* Add Company Category Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Company Category</DialogTitle>
            <DialogDescription>
              Create a new company category for business classification.
            </DialogDescription>
          </DialogHeader>
          <CompanyCategoryForm
            onSubmit={handleCreateCompanyCategory}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Company Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Company Category</DialogTitle>
            <DialogDescription>
              Update the company category information and settings.
            </DialogDescription>
          </DialogHeader>
          {selectedCompanyCategory && (
            <CompanyCategoryForm
              defaultValues={selectedCompanyCategory}
              onSubmit={handleUpdateCompanyCategory}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Company Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedCompanyCategory?.name}"? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
