"use client";

import { useState, useEffect } from "react";
import { Plus, MoreH<PERSON>zontal, Pencil, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { PropertyForm } from "@/components/forms/property-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import propertiesService from "../../../services/properties.service";

export default function Properties() {
  const { toast } = useToast();
  const [properties, setProperties] = useState([]);
  const [channels, setChannels] = useState([]);
  const [mediums, setMediums] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState(null);

  // Fetch properties from API using the service
  const fetchProperties = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await propertiesService.getAllProperties({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const propertiesArray = Array.isArray(result.properties)
        ? result.properties
        : [];
      setProperties(propertiesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch properties",
        variant: "destructive",
      });
      console.error("Error fetching properties:", error);
      setProperties([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch channels and mediums for dropdowns
  const fetchDropdownData = async () => {
    try {
      const [channelsData, mediumsData] = await Promise.all([
        propertiesService.getChannels(),
        propertiesService.getMediums(),
      ]);
      setChannels(channelsData);
      setMediums(mediumsData);
    } catch (error) {
      console.error("Error fetching dropdown data:", error);
      toast({
        title: "Warning",
        description: "Failed to load channels and mediums",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchProperties();
    fetchDropdownData();
  }, []);

  const handleAddProperty = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditProperty = (property) => {
    setSelectedProperty(property);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProperty = (property) => {
    setSelectedProperty(property);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateProperty = async (data) => {
    try {
      await propertiesService.createProperty(data);
      await fetchProperties();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Property created successfully",
      });
    } catch (error) {
      console.error("Create property error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create property",
        variant: "destructive",
      });
    }
  };

  const handleUpdateProperty = async (data) => {
    try {
      await propertiesService.updateProperty(selectedProperty.id, data);
      await fetchProperties();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Property updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update property",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteProperty = async () => {
    try {
      await propertiesService.deleteProperty(selectedProperty.id);
      await fetchProperties();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Property deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete property",
        variant: "destructive",
      });
    }
  };

  const handleImportProperties = async (file) => {
    try {
      const result = await propertiesService.bulkImportProperties(file);

      await fetchProperties(); // Refresh the properties list
      toast({
        title: "Success",
        description: "Properties imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import properties from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Property Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "channelName",
      header: "Channel",
      cell: ({ row }) => {
        const channelName =
          row.original.channelName ||
          channels.find((c) => c.id === row.original.channelId)?.name ||
          "Unknown";
        return <div>{channelName}</div>;
      },
    },
    {
      accessorKey: "mediumName",
      header: "Medium",
      cell: ({ row }) => {
        const mediumName =
          row.original.mediumName ||
          mediums.find((m) => m.id === row.original.mediumId)?.name ||
          "Unknown";
        return <div>{mediumName}</div>;
      },
    },
    {
      accessorKey: "description",
      header: "Description",
    },
    {
      accessorKey: "price",
      header: "Price",
      cell: ({ row }) => {
        const price = row.getValue("price");
        const currency = row.original.currency || "INR";
        return price ? (
          <div>
            {currency} {price}
          </div>
        ) : (
          <div>-</div>
        );
      },
    },
    {
      accessorKey: "isActive",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("isActive");
        return (
          <Badge variant={isActive ? "success" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const property = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleEditProperty(property)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDeleteProperty(property)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Properties Management</CardTitle>
              <CardDescription>
                Manage properties for different channels and mediums
              </CardDescription>
            </div>
            <Button onClick={handleAddProperty}>
              <Plus className="mr-2 h-4 w-4" />
              Add Property
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={properties}
            searchKey="name"
            isLoading={isLoading}
            entityName="Properties"
            onImport={handleImportProperties}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/properties_template.xlsx"
            fileDescription="File should contain columns: name, channelId, mediumId, description, price, currency, status"
          />
        </CardContent>
      </Card>

      {/* Add Property Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Property</DialogTitle>
            <DialogDescription>
              Create a new property for a channel and medium.
            </DialogDescription>
          </DialogHeader>
          <PropertyForm
            onSubmit={handleCreateProperty}
            onCancel={() => setIsAddDialogOpen(false)}
            channels={channels}
            mediums={mediums}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Property Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Property</DialogTitle>
            <DialogDescription>
              Update the property information.
            </DialogDescription>
          </DialogHeader>
          {selectedProperty && (
            <PropertyForm
              defaultValues={selectedProperty}
              onSubmit={handleUpdateProperty}
              onCancel={() => setIsEditDialogOpen(false)}
              channels={channels}
              mediums={mediums}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Property Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Property</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this property? This action cannot
              be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProperty}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
