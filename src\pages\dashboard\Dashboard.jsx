"use client";

import { useState, useEffect } from "react";
import {
  Line,
  LineChart,
  Bar,
  BarChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { ArrowUpRight, ArrowDownRight, X } from "lucide-react";
import dashboardService from "../../services/dashboard.service.js";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "../../components/ui/chart";
import { MobileHeader } from "../../components/mobile-header";
import { formatCurrency } from "../../lib/utils";
import { Badge } from "../../components/ui/badge";
import { Checkbox } from "../../components/ui/checkbox";
import { SidebarProvider } from "../../components/ui/sidebar";
import { TRANSACTION_STAGES } from "../../constants";

const Dashboard = () => {
  const [yearFilters, setYearFilters] = useState([]);
  const [monthFilters, setMonthFilters] = useState([]);
  const [cityFilters, setCityFilters] = useState([]);
  const [stateFilters, setStateFilters] = useState([]);
  const [zoneFilters, setZoneFilters] = useState([]);
  const [channelFilters, setChannelFilters] = useState([]);
  const [mediumFilters, setMediumFilters] = useState([]);
  const [variantFilters, setVariantFilters] = useState([]);
  const [contactStatusFilters, setContactStatusFilters] = useState([]);
  const [contactFilters, setContactFilters] = useState([]);
  const [stageFilters, setStageFilters] = useState([]);

  // Data states
  const [dashboardData, setDashboardData] = useState({
    summary: { totalRevenue: 0, previousYearRevenue: 0, trendPercentage: 0 },
    revenueOverTime: [],
    yearComparison: [],
    revenueByVariant: [],
    topClients: [],
  });

  // Filter options states
  const [filterOptions, setFilterOptions] = useState({
    years: ["2023", "2024", "2025"],
    months: [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ],
    mediums: ["Print", "Digital", "Outdoor", "Event"],
    channels: ["OA Mag", "New Media", "Events", "R4G", "M4G"],
    properties: ["Website", "Magazine", "Newsletter", "Convention", "Awards"],
    variants: ["Print", "Website", "Event", "Event Sp", "Seorc", "Mont"],
    cities: ["Mumbai", "Bengaluru", "Delhi", "Pune", "Chennai", "Hyderabad"],
    states: [
      "Maharashtra",
      "Karnataka",
      "Delhi",
      "Tamil Nadu",
      "Telangana",
      "Gujarat",
    ],
    zones: ["North", "South", "East", "West", "Central"],
    customers: [
      "Xireme Media",
      "Singpost",
      "SabRentKaro",
      "JBM Group",
      "Sakal",
    ],
    stages: TRANSACTION_STAGES,
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);

        // Build filters object with arrays for the new consolidated API
        const filters = {
          yearFilters,
          monthFilters,
          mediumFilters,
          channelFilters,
          variantFilters,
          cityFilters,
          stateFilters,
          zoneFilters,
          contactFilters,
          contactStatusFilters,
          stageFilters,
        };

        // Load all dashboard data using the new consolidated API
        const data = await dashboardService.getAllDashboardData(filters);
        setDashboardData(data);

        // Update filter options from the API response
        if (data.filterOptions) {
          console.log(data.filterOptions, "data.filterOptions");
          setFilterOptions(data.filterOptions);
        }
      } catch (error) {
        console.error("Error loading dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [
    yearFilters,
    monthFilters,
    mediumFilters,
    channelFilters,
    variantFilters,
    cityFilters,
    stateFilters,
    zoneFilters,
    contactFilters,
    contactStatusFilters,
    stageFilters,
  ]);

  const TrendIndicator = ({ value }) => (
    <div className="flex items-center text-xs">
      {value >= 0 ? (
        <>
          <ArrowUpRight className="mr-1 h-4 w-4 text-emerald-500" />
          <span className="text-emerald-500 font-medium">
            +{value.toFixed(1)}%
          </span>
        </>
      ) : (
        <>
          <ArrowDownRight className="mr-1 h-4 w-4 text-rose-500" />
          <span className="text-rose-500 font-medium">{value.toFixed(1)}%</span>
        </>
      )}
    </div>
  );

  const FilterSection = ({
    title,
    options,
    selectedValues,
    setSelectedValues,
  }) => (
    <div>
      <p className="text-sm font-medium mb-1">{title}</p>
      <div className="relative">
        <Select
          value={selectedValues.length === 0 ? "all" : selectedValues[0]}
          onValueChange={(value) => {
            if (value === "all") {
              setSelectedValues([]);
            } else if (selectedValues.includes(value)) {
              setSelectedValues(
                selectedValues.filter((item) => item !== value)
              );
            } else {
              setSelectedValues([
                ...selectedValues.filter((item) => item !== "all"),
                value,
              ]);
            }
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`All ${title}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            {options?.map((option) => (
              <SelectItem key={option} value={option}>
                <div className="flex items-center">
                  <Checkbox
                    checked={selectedValues.includes(option)}
                    className="mr-2"
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedValues([...selectedValues, option]);
                      } else {
                        setSelectedValues(
                          selectedValues.filter((item) => item !== option)
                        );
                      }
                    }}
                  />
                  {option}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {selectedValues.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-1">
            {selectedValues?.map((item) => (
              <Badge key={item} variant="secondary" className="text-xs">
                {item}
                <button
                  className="ml-1 text-xs"
                  onClick={() =>
                    setSelectedValues(selectedValues.filter((i) => i !== item))
                  }
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex flex-col">
        <MobileHeader />
        <div className="border-b hidden md:block">
          <div className="flex h-16 items-center px-4">
            <h1 className="text-lg font-semibold">Dashboard</h1>
          </div>
        </div>
        <div className="flex-1 p-4 md:p-6">
          <div className="md:hidden mb-6">
            <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
          </div>

          {isLoading && (
            <div className="flex items-center justify-center h-64">
              <div className="text-lg">Loading dashboard data...</div>
            </div>
          )}

          {!isLoading && (
            <>
              {/* Filters */}
              <Card className="mb-6">
                <CardContent className="p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-11 gap-4">
                    <FilterSection
                      title="Year"
                      options={filterOptions.years}
                      selectedValues={yearFilters}
                      setSelectedValues={setYearFilters}
                    />
                    <FilterSection
                      title="Month"
                      options={filterOptions.months}
                      selectedValues={monthFilters}
                      setSelectedValues={setMonthFilters}
                    />
                    <FilterSection
                      title="City"
                      options={filterOptions.cities}
                      selectedValues={cityFilters}
                      setSelectedValues={setCityFilters}
                    />
                    <FilterSection
                      title="State"
                      options={filterOptions.states}
                      selectedValues={stateFilters}
                      setSelectedValues={setStateFilters}
                    />
                    <FilterSection
                      title="Zone"
                      options={filterOptions.zones}
                      selectedValues={zoneFilters}
                      setSelectedValues={setZoneFilters}
                    />
                    <FilterSection
                      title="Channel"
                      options={filterOptions.channels}
                      selectedValues={channelFilters}
                      setSelectedValues={setChannelFilters}
                    />
                    <FilterSection
                      title="Medium"
                      options={filterOptions.mediums}
                      selectedValues={mediumFilters}
                      setSelectedValues={setMediumFilters}
                    />
                    <FilterSection
                      title="Variant"
                      options={filterOptions.variants}
                      selectedValues={variantFilters}
                      setSelectedValues={setVariantFilters}
                    />
                    <FilterSection
                      title="Properties"
                      options={
                        filterOptions.properties || [
                          "Active",
                          "Inactive",
                          "Pending",
                        ]
                      }
                      selectedValues={contactStatusFilters}
                      setSelectedValues={setContactStatusFilters}
                    />
                    <FilterSection
                      title="Company"
                      options={filterOptions.customers}
                      selectedValues={contactFilters}
                      setSelectedValues={setContactFilters}
                    />
                    <FilterSection
                      title="Stages"
                      options={filterOptions.stages || TRANSACTION_STAGES}
                      selectedValues={stageFilters}
                      setSelectedValues={setStageFilters}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Dashboard Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Total Revenue */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      Total Revenue Till Date
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col">
                      <p className="text-3xl font-bold">
                        {formatCurrency(dashboardData.summary.totalRevenue)}
                      </p>
                      <div className="flex items-center text-xs text-muted-foreground mt-2">
                        <TrendIndicator
                          value={dashboardData.summary.trendPercentage}
                        />
                        <span className="ml-1">vs. previous year</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Revenue Over Time This Year */}
                <Card className="md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      Revenue Over Time (This Year)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px] w-full overflow-hidden">
                      <ChartContainer
                        config={{
                          revenue: {
                            label: "Revenue",
                            color: "hsl(215, 70%, 60%)",
                          },
                        }}
                        className="h-full"
                      >
                        <ResponsiveContainer width="99%" height="99%">
                          <LineChart
                            data={dashboardData.revenueOverTime}
                            margin={{
                              top: 10,
                              right: 10,
                              left: 10,
                              bottom: 10,
                            }}
                          >
                            <CartesianGrid
                              strokeDasharray="3 3"
                              vertical={false}
                            />
                            <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                            <YAxis
                              tickFormatter={(value) => `${value / 1000}k`}
                              tick={{ fontSize: 12 }}
                            />
                            <ChartTooltip
                              content={
                                <ChartTooltipContent
                                  formatter={(value) =>
                                    formatCurrency(Number(value))
                                  }
                                />
                              }
                            />
                            <Line
                              type="monotone"
                              dataKey="revenue"
                              stroke="var(--color-revenue)"
                              strokeWidth={2}
                              dot={{ r: 3 }}
                              activeDot={{ r: 5 }}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>

                {/* Year-over-Year Comparison */}
                <Card className="md:col-span-3">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      Year-over-Year Revenue Comparison
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[250px] w-full overflow-hidden">
                      <ChartContainer
                        config={{
                          currentYear: {
                            label: "Current Year",
                            color: "hsl(215, 70%, 60%)",
                          },
                          previousYear: {
                            label: "Previous Year",
                            color: "hsl(215, 30%, 70%)",
                          },
                        }}
                        className="h-full"
                      >
                        <ResponsiveContainer width="99%" height="99%">
                          <BarChart
                            data={dashboardData.yearComparison}
                            margin={{
                              top: 10,
                              right: 10,
                              left: 10,
                              bottom: 10,
                            }}
                          >
                            <CartesianGrid
                              strokeDasharray="3 3"
                              vertical={false}
                            />
                            <XAxis dataKey="month" tick={{ fontSize: 12 }} />
                            <YAxis
                              tickFormatter={(value) => `${value / 1000}k`}
                              tick={{ fontSize: 12 }}
                            />
                            <ChartTooltip
                              content={
                                <ChartTooltipContent
                                  formatter={(value) =>
                                    formatCurrency(Number(value))
                                  }
                                />
                              }
                            />
                            <Bar
                              dataKey="currentYear"
                              fill="var(--color-currentYear)"
                              name="Current Year"
                            />
                            <Bar
                              dataKey="previousYear"
                              fill="var(--color-previousYear)"
                              name="Previous Year"
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>

                {/* Revenue by Medium/Channel/Property */}
                <Card className="md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      Revenue by Medium/Channel/Property
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[250px] w-full overflow-hidden">
                      <ChartContainer
                        config={{
                          revenue: {
                            label: "Revenue",
                            color: "hsl(215, 70%, 60%)",
                          },
                        }}
                        className="h-full"
                      >
                        <ResponsiveContainer width="99%" height="99%">
                          <BarChart
                            data={dashboardData.revenueByVariant}
                            margin={{ top: 5, right: 10, left: 5, bottom: 5 }}
                          >
                            <CartesianGrid
                              strokeDasharray="3 3"
                              vertical={false}
                            />
                            <XAxis dataKey="variant" tick={{ fontSize: 12 }} />
                            <YAxis
                              tickFormatter={(value) => `${value / 1000}k`}
                              tick={{ fontSize: 12 }}
                            />
                            <ChartTooltip
                              content={
                                <ChartTooltipContent
                                  formatter={(value) =>
                                    formatCurrency(Number(value))
                                  }
                                />
                              }
                            />
                            <Bar dataKey="revenue" fill="hsl(215, 70%, 60%)">
                              {dashboardData.revenueByVariant?.map(
                                (_, index) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={`hsl(215, ${70 - index * 5}%, ${
                                      60 - index * 3
                                    }%)`}
                                  />
                                )
                              )}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </ChartContainer>
                    </div>
                  </CardContent>
                </Card>

                {/* Top 5 Clients by Revenue */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      Top 5 Clients by Revenue
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {dashboardData.topClients?.map((item, index) => (
                        <div key={index} className="flex flex-col">
                          <div className="flex justify-between items-center">
                            <span>{item.company}</span>
                            <span className="font-medium">
                              {formatCurrency(item.revenue)}
                            </span>
                          </div>
                          <div className="flex justify-end mt-1">
                            <TrendIndicator value={item.trend} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Dashboard;
