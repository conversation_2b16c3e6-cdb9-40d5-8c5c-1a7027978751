"use client";

import { useState, useEffect } from "react";
import {
  Plus,
  MoreHorizontal,
  Pencil,
  Trash2,
  Users,
  Building,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { CustomerEmployeeForm } from "@/components/forms/customer-employee-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import customerEmployeeService from "../../../services/customer-employees.service";
import customerService from "../../../services/customer.service";

export default function CustomerEmployees() {
  const { toast } = useToast();
  const [customerEmployees, setCustomerEmployees] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  // Fetch customer employees from API
  const fetchCustomerEmployees = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await customerEmployeeService.getAllCustomerEmployees({
        page,
        limit,
        search,
      });

      const employeesArray = Array.isArray(result.customerEmployees)
        ? result.customerEmployees
        : [];
      setCustomerEmployees(employeesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch Contacts",
        variant: "destructive",
      });
      console.error("Error fetching Contacts:", error);
      setCustomerEmployees([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch customers for display
  const fetchCustomers = async () => {
    try {
      const result = await customerService.getAllCustomers({ limit: 1000 });
      setCustomers(result.customers || []);
    } catch (error) {
      console.error("Error fetching customers:", error);
    }
  };

  useEffect(() => {
    fetchCustomerEmployees();
    fetchCustomers();
  }, []);

  const handleAddEmployee = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditEmployee = (employee) => {
    setSelectedEmployee(employee);
    setIsEditDialogOpen(true);
  };

  const handleDeleteEmployee = (employee) => {
    setSelectedEmployee(employee);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateEmployee = async (data) => {
    try {
      await customerEmployeeService.createCustomerEmployee(data);
      await fetchCustomerEmployees();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact created successfully",
      });
    } catch (error) {
      console.error("Create contact error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create Contact",
        variant: "destructive",
      });
    }
  };

  const handleUpdateEmployee = async (data) => {
    try {
      await customerEmployeeService.updateCustomerEmployee(
        selectedEmployee.id,
        data
      );
      await fetchCustomerEmployees();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update Contact",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteEmployee = async () => {
    try {
      await customerEmployeeService.deleteCustomerEmployee(selectedEmployee.id);
      await fetchCustomerEmployees();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete Contact",
        variant: "destructive",
      });
    }
  };

  const handleImportEmployees = async (file) => {
    try {
      const result = await customerEmployeeService.bulkImportCustomerEmployees(
        file
      );

      await fetchCustomerEmployees();
      toast({
        title: "Success",
        description: "Contacts imported successfully",
      });

      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import Contacts from file",
        variant: "destructive",
      });
      throw error;
    }
  };

  const getStatusBadge = (status) => {
    return status === "active" ? (
      <Badge className="bg-emerald-500">Active</Badge>
    ) : (
      <Badge variant="destructive">Inactive</Badge>
    );
  };

  const getCustomerName = (customerId) => {
    const customer = customers.find(
      (c) => c.id.toString() === customerId?.toString()
    );
    return customer ? customer.name : "N/A";
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "customerId",
      header: "Company",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Building className="h-4 w-4 text-muted-foreground" />
          {getCustomerName(row.getValue("customerId"))}
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => {
        const email = row.getValue("email");
        return <div>{email || "N/A"}</div>;
      },
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: ({ row }) => {
        const phone = row.getValue("phone");
        return <div>{phone || "N/A"}</div>;
      },
    },
    {
      accessorKey: "designation",
      header: "Designation",
      cell: ({ row }) => {
        const designation = row.getValue("designation");
        return <div>{designation || "N/A"}</div>;
      },
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => {
        const department = row.getValue("department");
        return <div>{department || "N/A"}</div>;
      },
    },
    {
      accessorKey: "isPrimary",
      header: "Primary",
      cell: ({ row }) => {
        const isPrimary = row.getValue("isPrimary");
        return isPrimary ? (
          <Badge variant="outline">Primary</Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.getValue("status")),
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const employee = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditEmployee(employee)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteEmployee(employee)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Contacts Management</CardTitle>
              <CardDescription>
                Manage Contacts and their contact information
              </CardDescription>
            </div>
            <Button onClick={handleAddEmployee}>
              <Plus className="mr-2 h-4 w-4" />
              Add Contact
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={customerEmployees}
            searchKey="name"
            isLoading={isLoading}
            entityName="Contacts"
            onImport={handleImportEmployees}
            acceptedFileTypes=".xlsx,.xls,.csv"
            templatePath="/templates/customer_employees_template.xlsx"
            fileDescription="File should contain columns: customerId, name, email, phone, designation, department, isPrimary, status, notes"
          />
        </CardContent>
      </Card>

      {/* Add Employee Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Add New Contact</DialogTitle>
            <DialogDescription>
              Create a new Contact record with their contact details.
            </DialogDescription>
          </DialogHeader>
          <CustomerEmployeeForm
            onSubmit={handleCreateEmployee}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Employee Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Edit Contact</DialogTitle>
            <DialogDescription>
              Update the Contact information and contact details.
            </DialogDescription>
          </DialogHeader>
          {selectedEmployee && (
            <CustomerEmployeeForm
              defaultValues={selectedEmployee}
              onSubmit={handleUpdateEmployee}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Employee Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedEmployee?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteEmployee}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
