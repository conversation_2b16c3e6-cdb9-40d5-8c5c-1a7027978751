"use client";

import { useState, useEffect } from "react";
import { Plus, More<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { ContactForm } from "@/components/forms/contact-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle, 
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import contactsService from "../../../services/contacts.service";

export default function Contacts() {
  const { toast } = useToast();
  const [contacts, setContacts] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);

  // Fetch contacts from API using the service
  const fetchContacts = async (page = 1, limit = 10, search = "") => {
    try {
      setIsLoading(true);
      const result = await contactsService.getAllContacts({
        page,
        limit,
        search,
      });

      // Ensure we have an array and handle potential data format issues
      const contactsArray = Array.isArray(result.contacts)
        ? result.contacts
        : [];
      setContacts(contactsArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch contacts",
        variant: "destructive",
      });
      console.error("Error fetching contacts:", error);
      setContacts([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchContacts();
  }, []);

  const handleAddContact = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditContact = (contact) => {
    setSelectedContact(contact);
    setIsEditDialogOpen(true);
  };

  const handleDeleteContact = (contact) => {
    setSelectedContact(contact);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateContact = async (data) => {
    try {
      await contactsService.createContact(data);
      await fetchContacts();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact created successfully",
      });
    } catch (error) {
      console.error("Create contact error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create contact",
        variant: "destructive",
      });
    }
  };

  const handleUpdateContact = async (data) => {
    try {
      await contactsService.updateContact(selectedContact.id, data);
      await fetchContacts();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to update contact",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteContact = async () => {
    try {
      await contactsService.deleteContact(selectedContact.id);
      await fetchContacts();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Contact deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to delete contact",
        variant: "destructive",
      });
    }
  };

  const handleImportContacts = async (file) => {
    try {
      const result = await contactsService.bulkImportContacts(file);

      await fetchContacts(); // Refresh the contacts list
      toast({
        title: "Success",
        description: "Contacts imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message ||
          "Failed to import contacts from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error state
    }
  };

  const getStatusBadgeVariant = (status) => {
    switch (status) {
      case "new":
        return "default";
      case "in_progress":
        return "secondary";
      case "resolved":
        return "success";
      case "closed":
        return "outline";
      default:
        return "default";
    }
  };

  const getPriorityBadgeVariant = (priority) => {
    switch (priority) {
      case "low":
        return "outline";
      case "medium":
        return "secondary";
      case "high":
        return "default";
      case "urgent":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
      cell: ({ row }) => {
        const phone = row.getValue("phone");
        return <div>{phone || "N/A"}</div>;
      },
    },
    {
      accessorKey: "company",
      header: "Company",
      cell: ({ row }) => {
        const company = row.getValue("company");
        return <div>{company || "N/A"}</div>;
      },
    },
    {
      accessorKey: "message",
      header: "Message",
      cell: ({ row }) => {
        const message = row.getValue("message");
        return <div className="max-w-xs truncate">{message || "N/A"}</div>;
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={getStatusBadgeVariant(status)}>
            {status?.replace("_", " ").toUpperCase()}
          </Badge>
        );
      },
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.getValue("priority");
        return (
          <Badge variant={getPriorityBadgeVariant(priority)}>
            {priority?.toUpperCase()}
          </Badge>
        );
      },
    },
    {
      accessorKey: "assignedTo",
      header: "Assigned To",
      cell: ({ row }) => {
        const assignedTo = row.getValue("assignedTo");
        return <div>{assignedTo || "Unassigned"}</div>;
      },
    },
    {
      accessorKey: "tags",
      header: "Tags",
      cell: ({ row }) => {
        const tags = row.getValue("tags");
        if (!tags) return <div>N/A</div>;

        // Handle both string and array formats
        const tagArray = Array.isArray(tags)
          ? tags
          : typeof tags === "string"
          ? tags.split(",").map((t) => t.trim())
          : [];

        return (
          <div className="flex flex-wrap gap-1">
            {tagArray.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tagArray.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{tagArray.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "notes",
      header: "Notes",
      cell: ({ row }) => {
        const notes = row.getValue("notes");
        return <div className="max-w-xs truncate">{notes || "N/A"}</div>;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const contact = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditContact(contact)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteContact(contact)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Contact Management</CardTitle>
              <CardDescription>
                Manage customer contacts and inquiries
              </CardDescription>
            </div>
            <Button onClick={handleAddContact}>
              <Plus className="mr-2 h-4 w-4" />
              Add Contact
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={contacts}
            searchKey="name"
            isLoading={isLoading}
            entityName="Contacts"
            onImport={handleImportContacts}
            templatePath="/templates/contacts_template.xlsx"
            acceptedFileTypes=".xlsx,.xls"
            fileDescription="File should contain columns: name, email, phone, company, message, status, priority, assignedTo, tags, notes"
          />
        </CardContent>
      </Card>

      {/* Add Contact Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Contact</DialogTitle>
            <DialogDescription>
              Create a new contact record for customer inquiries.
            </DialogDescription>
          </DialogHeader>
          <ContactForm
            onSubmit={handleCreateContact}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Contact Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Contact</DialogTitle>
            <DialogDescription>
              Update the contact information.
            </DialogDescription>
          </DialogHeader>
          {selectedContact && (
            <ContactForm
              defaultValues={selectedContact}
              onSubmit={handleUpdateContact}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Contact Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the
              contact "{selectedContact?.name}" and remove their data from our
              servers.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteContact}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
