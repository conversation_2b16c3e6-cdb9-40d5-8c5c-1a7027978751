"use client";

import { useState, useEffect } from "react";
import { Plus, More<PERSON><PERSON><PERSON>tal, <PERSON><PERSON>l, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/data-table";
import { ZoneForm } from "@/components/forms/zone-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import zoneService from "../../../services/zones.service";

export default function Zone() {
  const { toast } = useToast();
  const [zones, setZones] = useState([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState(null);

  // Fetch zones from API using the service
  const fetchZones = async () => {
    try {
      setIsLoading(true);
      const result = await zoneService.getAllZones();

      // Ensure we have an array and handle potential data format issues
      const zonesArray = Array.isArray(result) ? result : [];
      setZones(zonesArray);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch zones",
        variant: "destructive",
      });
      console.error("Error fetching zones:", error);
      setZones([]); // Set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchZones();
  }, []);

  const handleAddZone = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditZone = (Zone) => {
    setSelectedZone(Zone);
    setIsEditDialogOpen(true);
  };

  const handleDeleteZone = (Zone) => {
    setSelectedZone(Zone);
    setIsDeleteDialogOpen(true);
  };

  const handleCreateZone = async (data) => {
    try {
      await zoneService.createZone(data);
      await fetchZones();
      setIsAddDialogOpen(false);
      toast({
        title: "Success",
        description: "Zone created successfully",
      });
    } catch (error) {
      console.error("Create Zone error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create Zone",
        variant: "destructive",
      });
    }
  };

  const handleUpdateZone = async (data) => {
    try {
      await zoneService.updateZone(selectedZone.id, data);
      await fetchZones();
      setIsEditDialogOpen(false);
      toast({
        title: "Success",
        description: "Zone updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update Zone",
        variant: "destructive",
      });
    }
  };

  const confirmDeleteZone = async () => {
    try {
      await zoneService.deleteZone(selectedZone.id);
      await fetchZones();
      setIsDeleteDialogOpen(false);
      toast({
        title: "Success",
        description: "Zone deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete Zone",
        variant: "destructive",
      });
    }
  };

  const handleImportZones = async (file) => {
    try {
      const result = await zoneService.bulkImportZones(file);

      await fetchZones(); // Refresh the zones list
      toast({
        title: "Success",
        description: "Zones imported successfully",
      });

      // Return the result so DataImportExport can use it
      return result;
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import Failed",
        description:
          error.response?.data?.message || "Failed to import zones from file",
        variant: "destructive",
      });
      throw error; // Re-throw to let DataImportExport handle the error Zone
    }
  };

  const columns = [
    {
      accessorKey: "name",
      header: "Zone Name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status");
        return (
          <Badge variant={status === "active" ? "success" : "secondary"}>
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const zone = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEditZone(zone)}>
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteZone(zone)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Zone Management</CardTitle>
              <CardDescription>
                Manage zones
              </CardDescription>
            </div>
            <Button onClick={handleAddZone}>
              <Plus className="mr-2 h-4 w-4" />
              Add Zone
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={zones}
            searchKey="name"
            isLoading={isLoading}
            entityName="Zones"
            onImport={handleImportZones}
            templatePath="/templates/zones_template.xlsx"
            acceptedFileTypes=".xlsx,.xls,.csv"
            fileDescription="File should contain columns: name, description, status"
          />
        </CardContent>
      </Card>

      {/* Add Zone Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Zone</DialogTitle>
            <DialogDescription>
              Create a new Zone
            </DialogDescription>
          </DialogHeader>
          <ZoneForm
            onSubmit={handleCreateZone}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Zone Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Zone</DialogTitle>
            <DialogDescription>
              Update the Zone information.
            </DialogDescription>
          </DialogHeader>
          {selectedZone && (
            <ZoneForm
              defaultValues={selectedZone}
              onSubmit={handleUpdateZone}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Zone Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedZone?.name}"? This
              action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteZone}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
